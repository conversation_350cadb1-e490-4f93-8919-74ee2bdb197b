import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';

/** 基础资料表单 查看 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'staffCode',
      label: '员工编号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'staffName',
      label: '员工姓名',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'contactNumber',
      label: '联系电话',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      componentProps: {
        clearable: true,
      },
      fieldName: 'email',
      label: '邮箱',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'birthday',
      label: '出生日期',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'genderTypeLabel',
      label: '性别',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'idCard',
      label: '身份证号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'graduationInstitution',
      label: '毕业院校',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'educationalLevelLabel',
      label: '学历',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'placeOriginName',
      label: '籍贯',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'ethnicGroupLabel',
      label: '民族',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'politicalStatusLabel',
      label: '政治面貌',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'residentialAddress',
      formItemClass: 'col-span-full',
      label: '联系地址',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      modelPropName: 'modelValue',
      fieldName: 'username',
      formItemClass: 'col-span-1',
      label: '关联用户账号',
    },
    {
      component: (props: any) => {
        return props.modelValue
          ? h(ImageViewer, {
              imgId: props.modelValue,
              imgCss: 'size-40',
              imgFit: 'cover',
              class: '!w-[160px]',
            })
          : h('div', null, '/');
      },
      modelPropName: 'imgId',
      fieldName: 'avatarId',
      formItemClass: 'col-span-full',
      label: '头像',
    },
  ];
}
