import { computed, defineComponent, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import Edit from './edit.vue';
import View from './view.vue';

const ResponsibleListFormTable = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    View,
  },
  name: 'ResponsibleListFormTable',
  props: {
    customerCode: {
      default: '',
      type: String,
    },
    customerId: {
      default: '',
      type: String,
    },
    isView: {
      default: false,
      type: Boolean,
    },
  },
  setup(props, { expose }) {
    const isEdit = computed(() => !isEmpty(props.customerId));
    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();

    const getResponsibleListFormTableValues = async () => {
      if (componentRef.value?.onSubmitToFormTable) {
        return await componentRef.value.onSubmitToFormTable();
      }
      return null;
    };

    const handleEdit = async () => {
      componentRef.value.onSubmitToFormTable();
    };

    expose({
      getResponsibleListFormTableValues,
    });

    return {
      componentRef,
      currentComponent,
      handleEdit,
      isEdit,
    };
  },
  template: `
    <FormCard title="主要负责人信息">
      <template #titleMore v-if="isEdit && !isView">
        <ElButton type="primary" @click="handleEdit" size="small">修改</ElButton>
      </template>
      <template #default>
        <component
          ref="componentRef"
          :is="currentComponent"
          :customerCode="customerCode"
          :customerId="customerId"
          :isView="isView"
          :isEdit="isEdit"
        />
      </template>
    </FormCard>
  `,
});

export { ResponsibleListFormTable };
