<script lang="ts" setup>
import { defineProps, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getCustomerBaseInfo } from '#/api/base-info';

import { useBaseInfoFormSchema } from './data';

const props = defineProps({
  customerCode: {
    default: '',
    type: String,
  },
  customerId: {
    default: '',
    type: String,
  },
  formConfig: {
    default: () => {},
    type: Object,
  },
  isView: {
    default: false,
    type: Boolean,
  },
});

const loading = ref(false);

/** 基础资料表单 */
const [Form, BaseInfoFormApi] = useVbenForm({
  ...props.formConfig,
  schema: useBaseInfoFormSchema(props.isView),
});

/** 获取数据 */
async function loadData(customerCode: string, customerId: string) {
  try {
    loading.value = true;
    const BaseInfoFormRes = await getCustomerBaseInfo({
      customerCode,
      customerId,
    });

    BaseInfoFormApi.setValues(BaseInfoFormRes);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  if (!isEmpty(props.customerId) || !isEmpty(props.customerCode)) {
    loadData(props.customerCode, props.customerId);
  }
});
</script>

<template>
  <Form v-loading="loading" />
</template>
