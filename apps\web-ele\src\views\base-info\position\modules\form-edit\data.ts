import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { z } from '@girant/adapter';

import { getAllRoleList, getPositionList } from '#/api/base-info';
import { getDictItemList } from '#/api/common/dict';

/** 表单 */
export function useFormSchema(positionId?: string): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        disabled: !!positionId,
        placeholder: '系统默认自动生成',
      },
      dependencies: {
        rules(values: any) {
          if (values.positionCode) {
            return z.string().max(128, '岗位编号长度不能超过128');
          }
          return null;
        },
        triggerFields: ['positionCode'],
      },
      fieldName: 'positionCode',
      label: '岗位编号',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'positionName',
      label: '岗位名称',
      rules: z
        .string()
        .min(1, '请输入岗位名称')
        .max(100, '岗位名称长度不能超过100'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const PositionTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return PositionTypeList;
        },
        api: () => {
          return getDictItemList('basePositionType');
        },
        clearable: true,
        filterable: true,
      },
      fieldName: 'positionType',
      formItemClass: 'col-span-full',
      label: '岗位类型',
      rules: 'selectRequired',
    },
    {
      component: 'Input',
      fieldName: 'deptId',
      label: '所属部门',
      rules: 'selectRequired',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        clearable: true,
        filterable: true,
        placeholder: '请选择',
        afterFetch: (data: any) => {
          let res = data.map((item: any) => ({
            label: item.positionName,
            value: item.positionId,
            deptName: item.deptName,
            isEnable: item.isEnable,
          }));
          // 过滤当前岗位
          if (positionId) {
            res = res.filter((item: any) => item.value !== positionId);
          }
          return res;
        },
        api: () => {
          return getPositionList({
            isEnable: true,
          });
        },
      },
      renderComponentContent: () => ({
        default: ({ item }: { item: any }) => {
          return h(
            'div',
            {
              class: 'flex justify-between',
            },
            [
              h(
                'span',
                {},
                {
                  default: () => item.label,
                },
              ),
              h(
                'span',
                {
                  size: 'small',
                  class: 'ml-2 text-neutral-400',
                },
                {
                  default: () => item.deptName,
                },
              ),
            ],
          );
        },
      }),
      fieldName: 'parentId',
      label: '上级岗位',
    },
    {
      component: 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.positionDuty) {
            return z.string().max(1000, { message: '最多输入1000个字符' });
          }
          return null;
        },
        triggerFields: ['positionDuty'],
      },
      fieldName: 'positionDuty',
      formItemClass: 'col-span-full items-start',
      label: '岗位职责',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        maxCollapseTags: 20,
        'fit-input-width': 350,
        afterFetch: (data: any) => {
          const roleList = data.map((item: any) => ({
            label: item.roleName,
            value: item.roleId,
          }));
          return roleList;
        },
        api: () => {
          return getAllRoleList();
        },
      },
      fieldName: 'roleIdList',
      formItemClass: 'col-span-full',
      label: '关联角色',
      rules: 'selectRequired',
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
    },
  ];
}
