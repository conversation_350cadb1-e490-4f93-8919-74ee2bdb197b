import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { BaseDataMaterialApi } from '#/api/base-info';

import { h, markRaw } from 'vue';

import { useAccess } from '@vben/access';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElInputTag } from 'element-plus';

import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

const { hasAccessByCodes } = useAccess();
/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'materialCodeList',
      formItemClass: 'col-span-2',
      label: '母件编号',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'materialName',
      label: '母件名称',
    },

    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个版本号用回车分隔',
        max: 4,
      },
      fieldName: 'versionList',
      formItemClass: 'col-span-2 ml-[20px]',
      label: 'BOM版本号',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,

        showAllLevels: false,
      }),
      fieldName: 'createUser',
      label: '创建人',
      modelPropName: 'value',
    },

    {
      component: 'Input',
      fieldName: 'createTime',
      formItemClass: 'col-span-2',
      label: '创建时间',
    },
  ];
}

/** 表格 */
export function useColumns<T = BaseDataMaterialApi.BaseDataMaterial>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  const operationColumn = {
    align: 'center',
    cellRender: {
      attrs: {
        nameField: 'customerName',
        nameTitle: 'BOM管理',
        onClick: onActionClick,
      },
      name: 'CellOperation',
      options: [
        ...(hasAccessByCodes(['base:bom:query:detail']) ? ['view'] : []),
        ...(hasAccessByCodes(['base:bom:edit:add']) ? ['edit'] : []),
      ],
    },
    field: 'operation',
    fixed: 'right',
    title: '操作',
    width: 150,
  } as NonNullable<VxeTableGridOptions['columns']>[0];

  return [
    { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
    {
      field: 'pictureFileId',
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
          }),
      },
      title: '图片',
      width: 150,
    },

    {
      field: 'materialCode',
      title: '母件编号',
      width: 150,
    },

    {
      field: 'materialName',
      minWidth: 180,
      title: '母件名称',
    },

    {
      field: 'version',
      title: 'BOM版本号',
      width: 200,
    },
    {
      field: 'createUserName',
      minWidth: 180,
      title: '创建人',
    },
    {
      field: 'createTime',
      title: '创建时间',
      width: 180,
    },

    {
      cellRender: {
        attrs: {
          activeText: '已启用',
          beforeChange: onStatusChange,
          inactiveText: '已禁用',
        },
        name: hasAccessByCodes([
          'base:bom:enable:enable',
          'base:bom:enable:disable',
        ])
          ? 'CellSwitch'
          : 'CellTag',
      },
      field: 'isValid',
      title: '启用状态',
      width: 100,
    },
    ...(hasAccessByCodes(['base:bom:query:detail', 'base:bom:edit:add'])
      ? [operationColumn]
      : []),
  ];
}
