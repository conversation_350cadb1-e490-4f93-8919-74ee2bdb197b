import type { OnActionClickFn, VxeGridProps } from '@girant/adapter';

/** 客户标签信息表单表格 */
export interface RowType {
  contractId?: string;
  deliveryMethodLabels?: string;
  deliveryMethods: string;
  endTime: string;
  invoiceType: string;
  invoiceTypeLabel?: string;
  name: string;
  otherSettlementMethod: string;
  serialNumber?: string;
  settlementMethod: string;
  settlementMethodLabel?: string;
  startTime: string;
  vatInvoiceRate: string;
  vatInvoiceRateLabel?: string;
}

// 编辑表单表格
export const useGridOptions = <T = RowType>(
  isEdit: boolean,
  _onActionClick: OnActionClickFn<T> = () => {},
): Partial<VxeGridProps<T>> => {
  return {
    columns: [
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入',
          },
        },

        field: 'number',
        minWidth: 170,
        title: '合同编号',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入',
          },
        },
        field: 'name',
        minWidth: 170,
        title: '合同名称',
      },

      {
        editRender: {},
        field: 'startTime',
        slots: {
          edit: 'startTime',
        },
        title: '合同起始时间',
        width: 150,
      },

      {
        editRender: {},
        field: 'endTime',
        slots: {
          edit: 'endTime',
        },
        title: '合同结束时间',
        width: 150,
      },

      {
        field: 'settlementMethod',
        slots: {
          default: 'settlementMethod',
        },
        title: '合同结算方式',
        width: 150,
      },
      {
        field: 'invoiceType',
        slots: {
          default: 'invoiceType',
        },
        title: '发票类型',
        width: 150,
      },

      {
        field: 'vatInvoiceRate',
        slots: {
          default: 'vatInvoiceRate',
        },
        title: '发票汇率',
        width: 100,
      },
      {
        field: 'deliveryMethodList',
        slots: {
          default: 'deliveryMethodList',
        },
        title: '交货方式',
        width: 150,
      },

      {
        field: 'isValid',
        title: '合同有效性',
        width: 80,
      },

      {
        field: 'serialNumber',
        title: '附件',
        visible: false,
      },

      {
        field: 'contractId',
        title: '合同id',
        visible: false,
      },

      {
        slots: {
          default: 'Operation',
        },
        title: '操作',
        width: 280,
      },
    ],
    // editConfig: {
    //   trigger: 'manual',
    // },
    editRules: {
      name: [
        {
          content: '长度不超过128',
          max: 10,
          // trigger: 'blur',
        },
        {
          content: '不能为空',
          required: true,
        },
      ],
      number: [
        {
          content: '长度不超过128',
          max: 10,
          // trigger: 'blur',
        },
        {
          content: '不能为空',
          required: true,
        },
      ],
    },
  };
};

export const useViewGridOptions = <T = RowType>(): Partial<VxeGridProps<T>> => {
  return {
    border: true,
    columns: [
      { field: 'number', title: '合同编号' },
      { field: 'name', title: '合同名称' },
      { field: 'startTime', title: '合同起始时间' },
      { field: 'endTime', title: '合同结束时间' },
      { field: 'settlementMethodLabel', title: '合同结算方式' },
      { field: 'invoiceTypeLabel', title: '发票类型' },
      { field: 'vatInvoiceRateLabel', title: '发票汇率' },
      { field: 'deliveryMethodList', title: '交货方式' },
      { field: 'isValid', title: '合同有效性' },
    ],
    pagerConfig: {
      enabled: false,
    },
  };
};
