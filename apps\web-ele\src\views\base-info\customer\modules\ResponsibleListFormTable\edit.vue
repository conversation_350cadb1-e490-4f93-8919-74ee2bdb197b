<script lang="ts" setup>
import type { OnActionClickParams } from '@girant/adapter';

import type { RowType } from './data';

import { defineProps, nextTick, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { ElMessage, ElMessageBox } from 'element-plus';

import {
  getCustomerResponsible,
  modCustomerResponsible,
} from '#/api/base-info';

import { useGridOptions } from './data';

const props = defineProps({
  customerCode: {
    default: '',
    type: String,
  },
  customerId: {
    default: '',
    type: String,
  },
  isEdit: {
    default: false,
    type: Boolean,
  },
  isView: {
    default: false,
    type: Boolean,
  },
});

const loading = ref(false);
const gridTable = ref();
const gridTableData = ref<Array<RowType>>([]);

function onActionClick(e: OnActionClickParams) {
  switch (e.code) {
    case 'cancel': {
      gridTable.value.revertRow(e.row);
      gridTable.value.cancelRow(e.row);
      break;
    }
    case 'delete': {
      gridTable.value.removeRow(e.row);
      break;
    }
    case 'edit': {
      gridTable.value.editRow(e.row);
      break;
    }
  }
}
// 编辑表单表格
const gridOptions = useGridOptions(onActionClick);

/** 获取数据 */
async function loadData(customerId: string) {
  try {
    loading.value = true;
    const FormTableRes = await getCustomerResponsible({
      customerId,
    });

    if (!FormTableRes) {
      return;
    }

    gridTableData.value = FormTableRes;

    nextTick(() => {
      gridTable.value.setTableData(gridTableData.value);
    });

    return gridTableData.value;
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  nextTick(() => {
    if (!props.isView) {
      gridTable.value.setTableData([]);
    }
  });

  if (!isEmpty(props.customerId)) {
    loadData(props.customerId);
  }
});

// 获取表格数据
const getTableData = async () => {
  const tableData = await gridTable.value.getTableData();
  return tableData;
};

// 校验表格数据
const tableValidate = async () => {
  const tableValidateRes = await gridTable.value.tableValidate(true);
  return tableValidateRes;
};

const onSubmitToFormTable = async () => {
  const mergedValues: {
    customerId?: string;
    responsibleList: Array<RowType>;
  } = {
    responsibleList: [],
  };
  try {
    loading.value = true;
    const tableData = await getTableData();

    // 空表单检查 - 仅在新建模式下
    if (tableData.length === 0) {
      return null;
    }

    // 表单验证
    const tableValidateRes = await tableValidate();
    if (tableValidateRes) {
      if (props.isEdit) {
        // 编辑模式：显示错误消息
        ElMessage.error('请正确填写表单');
        return null;
      } else {
        // 新建模式：抛出错误给调用方
        throw new Error('客户标签信息表单验证失败');
      }
    }

    // 编辑模式
    if (props.isEdit) {
      mergedValues.customerId = props.customerId;
      mergedValues.responsibleList = tableData;
      if (
        await ElMessageBox.confirm('确定修改吗？', '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning',
        })
      ) {
        await modCustomerResponsible(mergedValues);
        ElMessage.success('编辑成功');
      }
      return null;
    }

    // 新建模式
    return tableData;
  } catch (error) {
    if (!props.isEdit && error instanceof Error) {
      // 新建模式下，将错误继续向上抛出
      throw error;
    } else {
      // 编辑模式错误处理
      ElMessage.error('编辑失败');
      console.error(error);
      return null;
    }
  } finally {
    loading.value = false;
  }
};

defineExpose({
  onSubmitToFormTable,
});
</script>

<template>
  <div v-loading="loading">
    <DynamicTable ref="gridTable" :grid-options="gridOptions" class="border" />
  </div>
</template>
