import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { BaseDataCustomerApi } from '#/api/base-info';

import { markRaw } from 'vue';

import { useAccess } from '@vben/access';

import { $t } from '@girant/locales';
import { ElInputTag } from 'element-plus';

import { getDictItemList } from '#/api/common';

const { hasAccessByCodes } = useAccess();

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'customerCodeList',
      formItemClass: 'col-span-2',
      label: '客户编号',
    },

    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'customerName',
      label: '客户名称',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'tagName',
      label: '客户标签',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'address',
      label: '客户地址',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const baseCompanyScaleTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return baseCompanyScaleTypeList;
        },
        api: () => {
          return getDictItemList('baseCompanyScale');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'companyScaleList',
      label: '企业规模',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const companyTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return companyTypeList;
        },
        api: () => {
          return getDictItemList('baseCompanyType');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'companyTypeList',
      label: '企业性质',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '全部', value: '' },
          { label: '正常', value: true },
          { label: '停用', value: false },
        ],
      },
      defaultValue: true,
      fieldName: 'isEnable',
      label: '客户状态',
    },
  ];
}
/** 表格 */
export function useColumns<T = BaseDataCustomerApi.BaseDataCustomer>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  const operationColumn = {
    align: 'center',
    cellRender: {
      attrs: {
        nameField: 'customerName',
        nameTitle: '客户管理',
        onClick: onActionClick,
      },
      name: 'CellOperation',
      options: [
        ...(hasAccessByCodes([
          'base:customer:query:basic',
          'base:customer:query:contacts',
          'base:customer:query:profile',
          'base:customer:query:leader',
          'base:customer:query:tag',
          'base:customer:query:trading',
        ])
          ? ['view']
          : []),
        ...(hasAccessByCodes([
          'base:customer:edit:modify:basic',
          'base:customer:edit:modify:tag',
          'base:customer:edit:modify:contacts',
          'base:customer:edit:modify:profile',
          'base:customer:edit:modify:leader',
          'base:customer:edit:modify:trading',
        ])
          ? ['edit']
          : []),
      ],
    },
    field: 'operation',
    fixed: 'right',
    title: '操作',
    width: 150,
  } as NonNullable<VxeTableGridOptions['columns']>[0];

  return [
    { align: 'left', type: 'checkbox', width: 30 },
    { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
    {
      field: 'customerCode',
      title: '客户编号',
      width: 150,
    },
    {
      field: 'customerName',
      minWidth: 200,
      title: '客户名称',
    },
    {
      field: 'customerEnName',
      minWidth: 150,
      title: '客户英文名',
    },

    {
      field: 'customerAbbr',
      minWidth: 120,
      title: '客户简称',
    },
    {
      field: 'tagList',
      minWidth: 350,
      showOverflow: false,
      slots: {
        default: 'tagList',
      },
      title: '客户标签',
    },
    {
      field: 'address',
      minWidth: 280,
      title: '地址信息',
    },
    {
      field: 'contactsPhone',
      title: '联系方式/固话',
      width: 150,
    },
    {
      field: 'contactPersonName',
      title: '联系人名称',
      width: 150,
    },
    {
      field: 'contactPersonPhone',
      title: '联系人电话',
      width: 150,
    },
    {
      field: 'contactPersonJobDesc',
      title: '联系人职务',
      width: 100,
    },
    {
      field: 'companyScaleLabel',
      title: '企业规模',
      width: 120,
    },

    {
      field: 'companyTypeLabel',
      title: '企业性质',
      width: 120,
    },
    {
      cellRender: {
        attrs: {
          activeText: $t('common.enabled'),
          beforeChange: onStatusChange,
          inactiveText: $t('common.disabled'),
        },
        name: hasAccessByCodes([
          'base:customer:enable:enable',
          'base:customer:enable:disable',
        ])
          ? 'CellSwitch'
          : 'CellTag',
      },
      field: 'isEnable',
      title: '客户状态',
      width: 100,
    },
    ...(hasAccessByCodes([
      'base:customer:query:basic',
      'base:customer:query:contacts',
      'base:customer:query:profile',
      'base:customer:query:leader',
      'base:customer:query:tag',
      'base:customer:query:trading',
      'base:customer:edit:modify:basic',
      'base:customer:edit:modify:tag',
      'base:customer:edit:modify:contacts',
      'base:customer:edit:modify:profile',
      'base:customer:edit:modify:leader',
      'base:customer:edit:modify:trading',
    ])
      ? [operationColumn]
      : []),
  ];
}
