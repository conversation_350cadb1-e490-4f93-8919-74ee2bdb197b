import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { z } from '@girant/adapter';

import { getDictItemList } from '#/api/common';

export function useTransactionInfoFormSchema(
  isViewMode: boolean,
): VbenFormSchema[] {
  return [
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'bankAccountName',
      label: '银行户名',
      rules: z
        .string()
        .min(1, '请输入银行户名')
        .max(200, '银行户名长度不超过200'),
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'bankName',
      label: '银行开户行',
      rules: z
        .string()
        .min(1, '请输入银行开户行')
        .max(200, '开户行长度不超过200'),
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'bankAccountNumber',
      label: '银行账号',
      rules: z
        .string()
        .min(1, '请输入银行账号')
        .max(30, '银行账号长度不超过30'),
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.taxRegistrationNumber) {
            return z.string().max(100, { message: '纳税登记号长度不超过100' });
          }
          return null;
        },
        triggerFields: ['taxRegistrationNumber'],
      },
      fieldName: 'taxRegistrationNumber',
      label: '纳税登记号',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.settlementCurrency) {
            return z.string().max(20, { message: '结算币种长度不超过20' });
          }
          return null;
        },
        triggerFields: ['settlementCurrency'],
      },
      fieldName: 'settlementCurrency',
      label: '结算币种',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: [], // 初始为空
      },
      defaultValue: '',
      dependencies: {
        async componentProps() {
          const data = await getDictItemList('baseSettlementMethod');
          return {
            options: data.map((item: any) => ({
              label: item.dictLabel,
              value: item.dictValue,
            })),
          };
        },
        triggerFields: [''],
      },
      fieldName: isViewMode ? 'settlementMethodLabel' : 'settlementMethod',
      formItemClass: 'col-start-1 col-span-full',
      label: '结算方式',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'settlementPeriod',
      formItemClass: 'col-span-2 col-start-1',
      label: '周期天数',
      suffix: () => {
        return isViewMode
          ? ''
          : h(
              'span',
              { class: 'text-nowrap' },
              '若选择月结结算方式需填写周期天数',
            );
      },
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.settlementCurrency) {
            return z.string().max(1000, { message: '其他说明长度不超过1000' });
          }
          return null;
        },
        triggerFields: ['otherSettlementMethod'],
      },
      fieldName: 'otherSettlementMethod',
      formItemClass: 'col-span-2 col-start-1',
      label: '其他说明',

      suffix: () => {
        return isViewMode
          ? ''
          : h(
              'span',
              { class: 'text-nowrap' },
              '若选择其他结算方式需填写其他说明',
            );
      },
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: [], // 初始为空
      },
      defaultValue: '',
      dependencies: {
        async componentProps() {
          const data = await getDictItemList('baseInvoiceType');
          return {
            options: data.map((item: any) => ({
              label: item.dictLabel,
              value: item.dictValue,
            })),
          };
        },
        triggerFields: [''],
      },
      fieldName: isViewMode ? 'invoiceTypeLabel' : 'invoiceType',
      formItemClass: 'col-start-1 col-span-1',
      label: '发票类型',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: [], // 初始为空
      },
      defaultValue: '',
      dependencies: {
        async componentProps() {
          const data = await getDictItemList('baseVatInvoiceRate');
          return {
            options: data.map((item: any) => ({
              label: item.dictLabel,
              value: item.dictValue,
            })),
          };
        },
        if(values) {
          if (values.invoiceType === '10') {
            return true;
          } else {
            values.vatInvoiceRate = '';
            return false;
          }
        },
        required(values) {
          if (values.invoiceType === '10') {
            return true;
          } else {
            values.vatInvoiceRate = '';
            return false;
          }
        },
        triggerFields: ['invoiceType'],
      },
      fieldName: 'vatInvoiceRate',
      formItemClass: 'col-span-2',
      label: '税率',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiCheckboxGroup',
      componentProps: {
        afterFetch: (data: any) => {
          const deliveryMethodList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return deliveryMethodList;
        },
        api: () => {
          return getDictItemList('baseDeliveryMethod');
        },
        class: 'flex flex-wrap',
      },
      defaultValue: [],
      fieldName: isViewMode ? 'deliveryMethodLabels' : 'deliveryMethodList',
      formItemClass: 'col-start-1',
      label: '交货方式',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, `${showText} 天`);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'supplyCycle',
      label: '供货周期',
      suffix: () => (isViewMode ? '' : h('span', null, '天')),
    },
  ];
}
