import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

/** 人事资料表单 查看*/
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'deptName',
      label: '所属部门',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'positionName',
      label: '任职岗位',
    },
    {
      component: 'div',
      fieldName: 'concurrentPositionIdList',
      label: '兼任其他岗位',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'upPositionName',
      label: '上级主管',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'hireTime',
      label: '入职日期',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'terminationTime',
      label: '离职日期',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'remark',
      formItemClass: 'col-span-full items-start',
      label: '备注',
    },
    {
      component: h(UploadFiles, {
        mode: 'readMode',
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
      wrapperClass: 'mt-[2px]',
    },
  ];
}
