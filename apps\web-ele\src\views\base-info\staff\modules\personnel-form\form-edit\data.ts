import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { z } from '@girant/adapter';
import { ElTag } from 'element-plus';

import {
  getAllDeptTree,
  getPositionDetail,
  getPositionList,
} from '#/api/base-info';

/** 人事资料表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiTreeSelect',
      renderComponentContent: () => ({
        default: ({ data }: { data: any }) => {
          return h(
            'div',
            {
              class: 'flex',
            },
            [
              h(
                'span',
                {},
                {
                  default: () => data.label,
                },
              ),
              data.isEnable
                ? null
                : h(
                    ElTag,
                    {
                      size: 'small',
                      class: 'ml-2',
                    },
                    {
                      default: () => '停用',
                    },
                  ),
            ],
          );
        },
      }),
      componentProps: {
        afterFetch: (data: any) => {
          // 转换函数
          function convertDeptData(dept: any) {
            return {
              label: `【${dept.deptCode}】${dept.deptName}`,
              value: dept.deptId,
              isEnable: dept.isEnable,
              children: dept.children
                ? dept.children.map((child: any) => convertDeptData(child))
                : [],
            };
          }
          // 执行转换
          const convertedData = data.map((dept: any) => convertDeptData(dept));
          return convertedData;
        },
        api: () => {
          return getAllDeptTree({});
        },
        checkStrictly: true,
        clearable: true,
        filterable: true,
      },
      fieldName: 'deptId',
      label: '所属部门',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        filterable: true,
        options: [],
        placeholder: '请先选择部门',
      },
      dependencies: {
        async componentProps(values) {
          if (values.deptId) {
            let res = await getPositionList({
              deptIdList: values.deptId,
              isEnable: true,
            });
            res = res.map((item: any) => ({
              label: item.positionName,
              value: item.positionId,
            }));
            // 检查res中是否有directorPositionId 的值，如果没有说明切换了部门,清空directorPositionId
            if (
              !res.some((item: any) => item.value === values.directorPositionId)
            ) {
              values.directorPositionId = '';
            }
            return {
              options: res,
            };
          } else {
            values.directorPositionId = '';
          }
          return {};
        },
        disabled(values) {
          return !values.deptId;
        },
        triggerFields: ['deptId'],
      },
      fieldName: 'directorPositionId',
      label: '任职岗位',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        filterable: true,
        maxCollapseTags: 1,
        multiple: true,
        options: [],
        placeholder: '请先选择岗位',
        fitInputWidth: 300,
      },
      renderComponentContent: () => ({
        default: ({ item }: { item: any }) => {
          return h(
            'div',
            {
              class: 'flex justify-between',
            },
            [
              h(
                'span',
                {},
                {
                  default: () => item.label,
                },
              ),
              h(
                'span',
                {
                  size: 'small',
                  class: 'ml-2 text-neutral-400',
                },
                {
                  default: () => item.deptName,
                },
              ),
            ],
          );
        },
      }),
      dependencies: {
        async componentProps(values) {
          if (!values.directorPositionId) {
            values.concurrentPositionIdList = '';
          }
          if (values.directorPositionId && values.deptId) {
            let res = await getPositionList({
              isEnable: true,
            });
            res = res.map((item: any) => ({
              label: item.positionName,
              value: item.positionId,
              deptName: item.deptName,
              isEnable: item.isEnable,
            }));
            // 过滤掉当前岗位
            res = res.filter(
              (item: any) => item.value !== values.directorPositionId,
            );
            return {
              options: res,
            };
          }
          return {};
        },
        disabled(values) {
          return !values.directorPositionId;
        },
        triggerFields: ['directorPositionId'],
      },
      fieldName: 'concurrentPositionIdList',
      label: '兼任其他岗位',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请先选择岗位',
      },
      dependencies: {
        async trigger(values: any) {
          values.upPositionName = '';
          if (values.directorPositionId) {
            const res = await getPositionDetail(values.directorPositionId);
            values.upPositionName = res?.upPositionName || '无上级主管';
          }
        },
        triggerFields: ['directorPositionId'],
      },
      disabled: true,
      fieldName: 'upPositionName',
      label: '上级主管',
    },
    {
      component: 'DatePicker',
      componentProps: {
        class: 'flex-auto',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'hireTime',
      label: '入职日期',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        class: 'flex-auto',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      dependencies: {
        rules(values: any) {
          // 入职时间不能大于离职时间
          if (values.hireTime > values.terminationTime) {
            return z.string().max(0, { message: '入职时间不能大于离职时间' });
          }
          return null;
        },
        triggerFields: ['required', 'terminationTime'],
      },
      fieldName: 'terminationTime',
      label: '离职日期',
    },
    {
      component: 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.remark) {
            return z.string().max(1000, { message: '最多输入1000个字符' });
          }
          return null;
        },
        triggerFields: ['remark'],
      },
      fieldName: 'remark',
      formItemClass: 'col-span-full items-start',
      label: '备注',
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
        class: 'w-full',
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
    },
  ];
}
