<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import { getDeptDetail, modDept, saveDept } from '#/api/base-info';

import { useFormSchema } from './data';

const props = defineProps({
  /** 部门id */
  deptId: {
    default: '',
    type: String,
  },
});
const emits = defineEmits(['deptFormSubmitCancel', 'deptFormSubmitSuccess']);
const loading = ref(false);
/** 统一提交处理*/
const onSubmit = async (values: Record<string, any>) => {
  // 等待文件上传完成
  const serialNumber: any = await formApi?.getFieldComponentRef('serialNumber');
  const isCompleted = await serialNumber?.getCompleteStatus();
  if (!isCompleted) {
    ElMessage.warning('请等待附件上传完成');
    return;
  }
  const isUpdate = !!props.deptId;
  const confirmText = isUpdate ? '确定更新吗？' : '确定提交吗？';
  if (await confirm(confirmText)) {
    try {
      loading.value = true;
      await (isUpdate ? modDept : saveDept)({
        ...values,
        deptId: props.deptId,
        // 如果没有选择父级，设置为顶级部门
        parentId: values.parentId || '0',
      });
      emits('deptFormSubmitSuccess');
      ElMessage.success(isUpdate ? '编辑成功' : '添加成功');
    } catch {
      ElMessage.error('操作失败');
    } finally {
      loading.value = false;
    }
  }
};
/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
  },
  handleSubmit: onSubmit,
  schema: useFormSchema(props.deptId),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

/** 确认对话框抽象*/
const confirm = (content: string) => {
  return ElMessageBox.confirm(content, '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  });
};

/** 获取数据 */
const loadData = async (deptId: string) => {
  try {
    loading.value = true;
    const data = await getDeptDetail({
      deptId,
    });
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  if (props.deptId) {
    loadData(props.deptId);
  }
});
defineExpose({
  formApi,
});
</script>
<template>
  <Form v-loading="loading" />
</template>
