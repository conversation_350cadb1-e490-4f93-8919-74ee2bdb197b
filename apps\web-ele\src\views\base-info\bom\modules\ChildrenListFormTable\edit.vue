<script lang="ts" setup>
import type { OnActionClickParams, VxeGridListeners } from '@girant/adapter';
import type { UploadRequestOptions } from 'element-plus';

import { computed, defineProps, nextTick, onMounted, ref } from 'vue';

import { downloadFileFromBlob } from '@vben/utils';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { ElMessage, ElMessageBox } from 'element-plus';

import { materielExcel, ProdMaterielTemplate } from '#/api/base-info';

import { useGridOptions } from './data';

const props = defineProps({
  isView: {
    default: false,
    type: Boolean,
  },
  materialCode: {
    default: '',
    type: String,
  },
  materialId: {
    default: '',
    type: String,
  },
  isEdit: {
    default: false,
    type: Boolean,
  },
});

const gridTable = ref();
const GridApi = ref();

// 选择的数据
const checkedData = ref([]);

onMounted(async () => {
  nextTick(() => {
    const [, VbenGridApi] = gridTable.value.getGridTableIns();
    VbenGridApi.value = VbenGridApi;
    GridApi.value = VbenGridApi.grid;

    if (!props.isView) {
      gridTable.value.setTableData([]);
    }
  });
});

/** 下载模板 */
async function getProdMaterielTemplate() {
  try {
    const blob = await ProdMaterielTemplate();
    downloadFileFromBlob({
      source: blob,
      fileName: '子料清单模板',
    });
  } catch {
    ElMessage.error('文件下载失败：');
  }
}

/** 数据导入*/
async function importMaterielExcel(options: UploadRequestOptions) {
  const { file, onSuccess } = options;
  try {
    const data = { file };
    const response = await materielExcel(data);
    onSuccess(response);
    if (response.length > 0) {
      const tableData = response.map((item: any) => ({
        ...item,
        materialId: {
          materialId: item.materialId,
          materialName: item.materialName,
        },
      }));
      GridApi.value.loadData(tableData);
    }
    ElMessage.success('数据导入成功');
  } catch {
    ElMessage.error('数据导入失败');
  }
}

const leftBtn = ref([
  {
    label: '导入',
    code: 'import',
  },
  {
    label: '下载导入模板',
    code: 'exportTemplateDownload',
  },
]);

const rightBtn = ref([
  {
    label: '还原',
    code: 'restore',
  },
  {
    label: '清空',
    code: 'clear',
  },
  {
    label: '删除',
    code: 'delete',
    disabled: computed(() => checkedData.value.length === 0),
  },
]);

const tableTopBtnAction = async (btn: any) => {
  try {
    if (
      !(await ElMessageBox.confirm('确定要操作吗？', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      }))
    ) {
      return false;
    }
    switch (btn.code) {
      case 'clear': {
        GridApi.value.remove();
        break;
      }
      case 'delete': {
        GridApi.value.remove(checkedData.value);
        checkedData.value = [];
        break;
      }
      case 'exportTemplateDownload': {
        await getProdMaterielTemplate();
        break;
      }
      case 'restore': {
        GridApi.value.revertData();
        break;
      }
    }
  } catch {
    return false;
  }
};

function onActionClick(e: OnActionClickParams) {
  switch (e.code) {
    case 'cancel': {
      gridTable.value.revertRow(e.row);
      gridTable.value.cancelRow(e.row);
      break;
    }
    case 'delete': {
      gridTable.value.removeRow(e.row);
      break;
    }
    case 'edit': {
      gridTable.value.editRow(e.row);
      break;
    }
  }
}

// 编辑表单表格
const gridOptions = useGridOptions(onActionClick);
const gridEvents: VxeGridListeners = {
  checkboxChange: () => {
    checkedData.value = GridApi.value.getCheckboxRecords();
  },
  checkboxAll: () => {
    checkedData.value = GridApi.value.getCheckboxRecords();
  },
};

async function loadData(data: [any]) {
  gridTable.value.setTableData(data);
}

// 获取表格数据
const getTableData = async () => {
  const tableData = await gridTable.value.getTableData();

  // 获取所有物料ID和对应的物料信息
  const materialMap = new Map();
  tableData.forEach((item: any) => {
    const id = item.materialId.materialId;
    if (materialMap.has(id)) {
      const data = materialMap.get(id);
      data.count++;
      data.names.push(item.materialId.materialName);
    } else {
      materialMap.set(id, {
        count: 1,
        names: [item.materialId.materialName],
      });
    }
  });

  // 找出所有重复的物料
  const duplicates = [...materialMap.entries()]
    .filter(([_, data]) => data.count > 1)
    .map(([_, data]) => data.names[0]);

  if (duplicates.length > 0) {
    ElMessage.error(`以下物料重复选择: ${duplicates.join('、')}`);
    return false;
  }

  return tableData;
};

// 校验表格数据
const tableValidate = async () => {
  const tableValidateRes = await gridTable.value.tableValidate(true);
  return tableValidateRes;
};

const onSubmitToFormTable = async () => {
  let tableData = await getTableData();

  if (!tableData) {
    throw new Error('请检查子料清单');
  }

  if (tableData.length === 0) {
    throw new Error('子料清单不能为空');
  }

  // 表单验证
  const tableValidateRes = await tableValidate();
  if (tableValidateRes) {
    throw new Error('子料清单表单验证失败');
  }

  tableData = tableData.map((item: any) => {
    return {
      materialId: item.materialId.materialId,
      usageQuantity: item.usageQuantity,
    };
  });

  return { children: tableData };
};

defineExpose({
  loadData,
  onSubmitToFormTable,
});
</script>

<template>
  <div>
    <div class="flex justify-between">
      <div class="flex">
        <template v-for="item in leftBtn" :key="item.code">
          <template v-if="item.code === 'import'">
            <ElUpload
              :show-file-list="false"
              :http-request="importMaterielExcel"
              accept=".xlsx,.xls"
            >
              <ElButton type="primary" link>
                {{ item.label }}
              </ElButton>
            </ElUpload>
          </template>
          <ElButton v-else type="primary" link @click="tableTopBtnAction(item)">
            {{ item.label }}
          </ElButton>
        </template>
      </div>

      <div class="flex">
        <template v-for="item in rightBtn" :key="item.code">
          <template v-if="item.code === 'restore'">
            <ElButton
              type="primary"
              link
              v-if="isEdit"
              @click="tableTopBtnAction(item)"
            >
              {{ item.label }}
            </ElButton>
          </template>
          <template v-else>
            <ElButton
              type="primary"
              link
              :disabled="item.disabled"
              @click="tableTopBtnAction(item)"
            >
              {{ item.label }}
            </ElButton>
          </template>
        </template>
      </div>
    </div>
    <DynamicTable
      ref="gridTable"
      :grid-options="gridOptions"
      class="border"
      :table-options="{
        gridEvents,
      }"
    />
  </div>
</template>
