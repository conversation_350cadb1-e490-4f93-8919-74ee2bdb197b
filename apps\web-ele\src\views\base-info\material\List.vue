<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';
import type { UploadRequestOptions } from 'element-plus';

import type { Recordable } from '@vben/types';

import type { BaseDataMaterialApi } from '#/api/base-info';

import { onMounted, ref } from 'vue';

import { useAccess } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlob } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElMessage,
  ElMessageBox,
  ElSwitch,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import {
  changeMaterial,
  disableMaterial,
  enableMaterial,
  exportMaterial,
  getMaterialCategoryTree,
  getMaterialPage,
  importMaterial,
  materialTemplate,
  noRecommendMaterial,
  recommendMaterial,
} from '#/api/base-info';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

/** 物料id */
const materialId = ref('');
/** 物料编号 */
const materialCode = ref('');
const isView = ref(false);
// 是否是编辑状态
const isEdit = ref(false);
const modalFormRef = ref();

const { hasAccessByCodes } = useAccess();

const props = {
  label: 'label',
  value: 'value',
  expandTrigger: 'hover',
  multiple: true,
  checkStrictly: true,
  emitPath: false,
};
const materialCategoryTreeData = ref([]);
const materialCategoryList = ref([]);

// 转换函数
function convertDeptData(data: any) {
  return {
    label: data.categoryName,
    value: data.categoryCode,
    children: data.children
      ? data.children.map((child: any) => convertDeptData(child))
      : [],
  };
}

onMounted(async () => {
  const res = await getMaterialCategoryTree({});
  materialCategoryTreeData.value = res.map((item: any) =>
    convertDeptData(item),
  );
});

/** 操作 */
const onActionClick = (
  e: OnActionClickParams<BaseDataMaterialApi.BaseDataMaterial>,
) => {
  switch (e.code) {
    case 'edit': {
      onEdit(e.row);
      break;
    }
    case 'view': {
      onView(e.row);
      break;
    }
  }
};

/** 切换可用状态 */
const onStatusChange = async (
  newStatus: Boolean,
  row: BaseDataMaterialApi.BaseDataMaterial,
) => {
  const isEnable: Recordable<string> = {
    false: '已禁用',
    true: '已启用',
  };
  try {
    await confirm(
      `您要将 【${row.materialName}】 的状态切换为 【${isEnable[newStatus.toString()]}】 吗？`,
    );
    await (row.isEnable
      ? disableMaterial(row.materialId)
      : enableMaterial(row.materialId));
    return true;
  } catch {
    return false;
  }
};

/** 切换推荐状态 */
const onDeprecatedChange =
  (newStatus: Boolean, row: BaseDataMaterialApi.BaseDataMaterial) =>
  async () => {
    const isEnable: Recordable<string> = {
      false: '推荐',
      true: '不推荐',
    };

    try {
      await confirm(
        `您要将 【${row.materialName}】 的状态切换为 【${isEnable[newStatus.toString()]}】 吗？`,
      );
      await (newStatus
        ? noRecommendMaterial(row.materialId)
        : recommendMaterial(row.materialId));
      return true;
    } catch {
      return false;
    }
  };

const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onBeforeClose: () => {
    materialId.value = '';
    materialCode.value = '';
    return true;
  },
  onConfirm: () => {
    modalFormRef.value.submitAllForm();
  },
  showCancelButton: true,
  showConfirmButton: true,
  closeOnClickModal: false,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[70px]',
    },
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      materialCategoryList.value = [];

      gridApi.query();
    },
    schema: useGridFormSchema(),
    showCollapseButton: false,
    wrapperClass:
      'grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  },
  gridOptions: {
    border: true,
    cellConfig: {
      height: 60,
    },
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          if (formValues.materialAttributeList.length > 0) {
            params.materialAttributeList =
              formValues.materialAttributeList.join(',');
          }

          if (formValues.materialTypeList.length > 0) {
            params.materialTypeList = formValues.materialTypeList.join(',');
          }

          if (materialCategoryList.value.length > 0) {
            params.materialCategoryList = materialCategoryList.value.join(',');
          }

          return await getMaterialPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...params,
          });
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    checkboxConfig: {
      checkMethod: ({ row }) => {
        return !row.isStandard;
      },
      visibleMethod: ({ row }) => {
        return !row.isStandard;
      },
    },

    rowConfig: {
      keyField: 'materialId',
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<BaseDataMaterialApi.BaseDataMaterial>,
});

/** 确认对话框抽象*/
const confirm = (content: string) => {
  return ElMessageBox.confirm(content, '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  });
};

/** 打开模态框 */
function openFormModal(title: string, showConfirmButton = true) {
  formModalApi
    .setState({
      showConfirmButton,
      title,
    })
    .open();
}

/** 编辑 */
function onEdit(row: BaseDataMaterialApi.BaseDataMaterial) {
  materialId.value = row.materialId;
  materialCode.value = row.materialCode;
  isView.value = false;
  isEdit.value = true;
  openFormModal('修改物料信息', true);
}

/** 新增 */
function onCreate() {
  materialId.value = '';
  materialCode.value = '';
  isView.value = false;
  isEdit.value = false;
  openFormModal('新增物料信息', true);
}

/** 查看 */
async function onView(row: BaseDataMaterialApi.BaseDataMaterial) {
  materialId.value = row.materialId;
  materialCode.value = row.materialCode;
  isView.value = true;
  isEdit.value = false;
  openFormModal('查看物料信息', false);
}

const setFormModalLoading = (load: boolean) => {
  formModalApi.setState({ loading: load });
};

/** 关闭表单 */
const setFormModalClose = (state: boolean = false) => {
  if (state) {
    formModalApi.close();
  }
  gridApi.query();
};

/** 下载模板 */
async function getmaterialTemplate() {
  try {
    const blob = await materialTemplate();
    downloadFileFromBlob({
      source: blob,
      fileName: '物料模板',
    });
  } catch {
    ElMessage.error('文件下载失败：');
  }
}

/** 是否覆盖已经存在的物料清单 */
const isOverwrite = ref(false);

/** 数据导入*/
async function importMaterialHandle(options: UploadRequestOptions) {
  const { file, onSuccess } = options;
  try {
    const data = { file };
    const response = await importMaterial({
      data,
      isOverwrite: isOverwrite.value,
    });
    isOverwrite.value = false;
    gridApi.query();
    onSuccess(response);
    ElMessage.success('数据导入成功');
  } catch {
    ElMessage.error('数据导入失败');
  }
}

/** 数据导出 */
async function exportMaterialHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    const blob = await exportMaterial(formValues);
    downloadFileFromBlob({
      source: blob,
      fileName: '物料管理列表',
    });
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}

const onNonStandardToStandard = async () => {
  const checkedData = gridApi.grid.getCheckboxRecords();
  if (checkedData.length === 0) {
    ElMessage.warning('请选择要转换的非标准物料');
    return;
  }
  const ids = checkedData.map((item) => item.materialId);

  try {
    await changeMaterial(ids.join(','));
    ElMessage.success('非标准物料批量转成标准物料成功');
    gridApi.query();
  } catch {
    ElMessage.error('非标准物料批量转成标准物料失败');
  }
};
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-8/12">
      <Form
        ref="modalFormRef"
        :material-id="materialId"
        :is-edit="isEdit"
        :material-code="materialCode"
        @is-submit="setFormModalLoading"
        @submit-state="setFormModalClose"
        :is-view="isView"
      />
    </FormModal>
    <Grid>
      <template #form-materialCategoryList>
        <el-cascader
          v-model="materialCategoryList"
          :options="materialCategoryTreeData"
          :props="props"
          collapse-tags
          collapse-tags-tooltip
          clearable
        />
      </template>

      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onCreate"
          v-access:code="[
            'base:material:standard:edit:add',
            'base:material:nonstandard:edit:add',
          ]"
        >
          新增物料信息
        </ElButton>
        <ElButton
          type="primary"
          @click="onNonStandardToStandard"
          v-access:code="'base:material:standard:trans'"
        >
          非标准转标准
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="下载导入模板"
          placement="top-start"
        >
          <ElButton circle @click="getmaterialTemplate" class="mr-2">
            <template #icon><span class="iconfont">&#xe678;</span></template>
          </ElButton>
        </ElTooltip>
        <ElPopover placement="bottom" width="180">
          <template #reference>
            <ElUpload
              :show-file-list="false"
              :http-request="importMaterialHandle"
              accept=".xlsx,.xls"
            >
              <ElButton
                circle
                class="mr-2"
                v-access:code="'base:material:import'"
              >
                <template #icon>
                  <span class="iconfont">&#xe621;</span>
                </template>
              </ElButton>
            </ElUpload>
          </template>
          <el-checkbox
            v-model="isOverwrite"
            label="覆盖已经存在的物料清单"
            size="small"
          />
        </ElPopover>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportMaterialHandle"
            v-access:code="'base:material:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
      <template #isDeprecated="{ row }">
        <div
          v-if="
            hasAccessByCodes([
              'base:material:recommend:enable',
              'base:material:recommend:disable',
            ])
          "
        >
          <ElSwitch
            inline-prompt
            active-text="推荐"
            inactive-text="不推荐"
            :active-value="false"
            :inactive-value="true"
            :before-change="onDeprecatedChange(!row.isDeprecated, row)"
            v-model="row.isDeprecated"
          />
        </div>
        <div v-else>
          <ElTag type="primary">
            {{ row.isDeprecated ? '不推荐' : '推荐' }}
          </ElTag>
        </div>
      </template>
    </Grid>
  </Page>
</template>
