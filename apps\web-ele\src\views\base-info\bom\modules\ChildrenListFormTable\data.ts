import type { OnActionClickParams, VxeGridProps } from '@girant/adapter';

import { h } from 'vue';

import { getMaterialDetail, getMaterialPage } from '#/api/base-info';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';
/** 表单表格 */
export interface RowType {
  [key: string]: any;
  materialCode: string;
  baseUnitLabel: string;
  materialAttributeLabel: string;
  materialId: string;
  materialName: string;
  materialSpecs: string;
  materialTypeLabel: string;
  usageQuantity: string;
}

interface TableRow {
  [key: string]: any;
  materialId: number | string;
  materialName: string;
  usageQuantity: number;
}

const fetchMaterial =
  () =>
  async ({
    keyword,
    pageNum,
    pageSize,
  }: {
    keyword: string;
    pageNum: number;
    pageSize: number;
  }) => {
    return await getMaterialPage({
      materialName: keyword,
      isEnable: true,
      pageNum,
      pageSize,
    });
  };

// 编辑表单表格
export function useGridOptions(
  onActionClick: (e: OnActionClickParams) => void,
): VxeGridProps<TableRow> {
  return {
    columns: [
      {
        type: 'checkbox',
        width: 50,
      },

      {
        editRender: {},
        field: 'materialId',
        slots: {
          default: ({ row }: { row: RowType }) => {
            return row.materialName
              ? `${row.materialName}【${row.materialCode}】`
              : '';
          },
          edit: ({ $table, row }: { $table: any; row: RowType }) => {
            return h(RemoteSearchSelect, {
              fetchMethod: fetchMaterial(),
              labelKey: 'materialName',
              modelValue: row.materialId,
              onChange: async (materialId) => {
                if (!materialId) {
                  $table.setRow(row, {
                    materialId: null,
                    materialName: null,
                    materialCode: null,
                    materialSpecs: null,
                    baseUnitLabel: null,
                    materialTypeLabel: null,
                    materialAttributeLabel: null,
                    usageQuantity: null,
                  });
                  return;
                }
                const materialDetailRes = await getMaterialDetail({
                  materialId,
                });

                const rowData = {
                  ...materialDetailRes,
                  materialId: {
                    materialId,
                    materialName: materialDetailRes.materialName,
                  },
                };
                $table.setRow(row, rowData);
              },
              subLabelKey: 'materialCode',
              valueKey: 'materialId',
            });
          },
        },
        title: '物料名称',
        width: 230,
      },

      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入',
          },
        },
        field: 'usageQuantity',
        title: '数量',
        width: 120,
      },
      { field: 'materialCode', title: '物料编号', visible: false },
      { field: 'materialSpecs', title: '物料规格' },
      { field: 'baseUnitLabel', title: '基本单位' },
      { field: 'materialTypeLabel', title: '物料大类' },
      { field: 'materialAttributeLabel', title: '物料属性' },

      {
        align: 'center',
        cellRender: {
          attrs: {
            onClick: onActionClick,
          },
          name: 'CellOperation',
          options: ['edit', 'cancel', 'delete'],
        },
        title: '操作',
        width: 150,
      },
    ],

    editRules: {
      materialId: [
        { message: '物料名称不能为空', required: true, trigger: 'blur' },
      ],
      usageQuantity: [
        { message: '数量不能为空', required: true, trigger: 'blur' },
      ],
    },
  };
}

export const useViewGridOptions = <T extends RowType = RowType>(): Partial<
  VxeGridProps<T>
> => {
  return {
    border: true,
    columns: [
      { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
      {
        field: 'materialName',
        title: '物料名称',
        slots: {
          default: ({ row }: { row: RowType }) => {
            return row.materialName
              ? `${row.materialName}【${row.materialCode}】`
              : '';
          },
        },
        minWidth: 200,
      },
      { field: 'usageQuantity', title: '数量' },
      { field: 'materialCode', title: '物料编号' },
      { field: 'materialSpecs', title: '物料规格' },
      { field: 'baseUnitLabel', title: '基本单位' },
      { field: 'materialTypeLabel', title: '物料大类' },
      { field: 'materialAttributeLabel', title: '物料属性' },
    ],
    pagerConfig: {
      enabled: false,
    },
  };
};
