import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { BaseDataDeptApi } from '#/api/base-info';

import { markRaw } from 'vue';

import { useAccess } from '@vben/access';

import { ElInputTag } from 'element-plus';

const { hasAccessByCodes } = useAccess();
/** 查询*/
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'deptCodeList',
      label: '部门编号',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'deptName',
      label: '部门名称',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '全部', value: '' },
          { label: '正常', value: true },
          { label: '停用', value: false },
        ],
      },
      defaultValue: true,
      fieldName: 'isEnable',
      label: '部门状态',
    },
  ];
}

/** 部门树表格 */
export function useTableFormSchema<T = BaseDataDeptApi.BaseDataDept>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  const operationColumn = {
    align: 'center',
    cellRender: {
      attrs: {
        nameField: 'deptName',
        nameTitle: '部门设置',
        onClick: onActionClick,
      },
      name: 'CellOperation',
      options: [
        ...(hasAccessByCodes(['base:dept:query:detail']) ? ['view'] : []),
        ...(hasAccessByCodes(['base:dept:edit:modify']) ? ['edit'] : []),
      ],
    },
    field: 'operation',
    fixed: 'right',
    title: '操作',
    width: 140,
    minWidth: 140,
  } as NonNullable<VxeTableGridOptions['columns']>[0];
  return [
    {
      field: 'deptCode',
      title: '部门编号',
      treeNode: true,
      headerAlign: 'center',
      align: 'left',
      width: 350,
    },
    {
      field: 'deptName',
      title: '部门名称',
      width: 150,
    },
    {
      field: 'deptDescribe',
      align: 'left',
      headerAlign: 'center',
      minWidth: 200,
      title: '部门介绍',
    },
    {
      cellRender: {
        attrs: {
          activeText: '已启用',
          beforeChange: onStatusChange,
          inactiveText: '已禁用',
        },
        name: hasAccessByCodes([
          'base:dept:enable:enable',
          'base:dept:enable:disable',
        ])
          ? 'CellSwitch'
          : 'CellTag',
      },
      field: 'isEnable',
      title: '启用状态',
      width: 130,
    },
    ...(hasAccessByCodes(['base:dept:query:detail', 'base:dept:edit:modify'])
      ? [operationColumn]
      : []),
  ];
}
