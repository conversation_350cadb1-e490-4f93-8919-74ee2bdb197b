<script setup lang="ts">
import { ref } from 'vue';

import { useVbenForm } from '@girant/adapter';

import { useFormSchema } from './data';

/** 文件上传ref*/
const fileRef = ref();
/** 文件列表回显控制 */
const uploadLoading = ref(false);
/** 人事资料表单 */
const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 xl:grid-cols-3',
});

defineExpose({
  fileRef,
  formApi,
  uploadLoading,
});
</script>

<template>
  <Form />
</template>
