<script lang="ts" setup>
import type { OnActionClickParams } from '@girant/adapter';

import { defineProps } from 'vue';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElTag } from 'element-plus';

import { getProdBomHistoryPage } from '#/api/base-info';
import FormCard from '#/components/form-card/Index.vue';

import { useGridOptions } from './data';

const props = defineProps({
  isView: {
    default: false,
    type: Boolean,
  },
  materialCode: {
    default: '',
    type: String,
  },
  materialId: {
    default: '',
    type: String,
  },
  currentBomId: {
    default: '',
    type: String,
  },
});

const emits = defineEmits(['changeViewBomId', 'editForBomId']);

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    border: true,
    cellConfig: {
      height: 60,
    },
    columns: useGridOptions(),
    // height: '300px',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: { page: any }) => {
          return await getProdBomHistoryPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            materialId: props.materialId,
          });
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'bomId',
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  },
});

function onActionClick(e: OnActionClickParams) {
  switch (e.code) {
    case 'editForBomId': {
      emits('editForBomId', e.row.bomId);

      break;
    }

    case 'view': {
      emits('changeViewBomId', e.row.bomId);
      break;
    }
  }
}

const loadHistoryListFormTable = () => {
  gridApi.query();
};

defineExpose({ loadHistoryListFormTable });
</script>

<template>
  <FormCard title="历史版本">
    <template #default>
      <Grid>
        <template #currentVersion="{ row }">
          <span class="ml-2">{{ row.currentVersion }}</span>
          <ElTag
            class="ml-2"
            effect="dark"
            type="primary"
            v-if="row.bomId === currentBomId"
          >
            当前查看版本
          </ElTag>
        </template>
        <template #isValid="{ row }">
          <ElTag v-if="row.isValid" type="success" effect="dark">
            使用中
          </ElTag>
          <ElTag v-else type="danger" effect="dark"> 停用 </ElTag>
        </template>

        <template #operation="{ row }">
          <ElButton
            link
            type="primary"
            v-if="row.bomId !== currentBomId"
            @click="onActionClick({ code: 'view', row })"
          >
            查看此版本
          </ElButton>
          <ElButton
            link
            type="primary"
            v-access:code="'base:bom:edit:add'"
            @click="onActionClick({ code: 'editForBomId', row })"
          >
            基于此版本发起变更
          </ElButton>
        </template>
      </Grid>
    </template>
  </FormCard>
</template>
