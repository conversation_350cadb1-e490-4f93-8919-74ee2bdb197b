import type { OnActionClickFn, VxeGridProps } from '@girant/adapter';

/** 联系人信息表单表格 */
export interface RowType {
  customerId?: string;
  jobDesc: string;
  remark: string;
  responsibleEmail: boolean;
  responsibleId?: string;
  responsibleName: string;
  responsiblePhone: string;
  responsibleWechat: string;
}

// 编辑表单表格
export const useGridOptions = <T = RowType>(
  _onActionClick: OnActionClickFn<T> = () => {},
): Partial<VxeGridProps<T>> => {
  return {
    columns: [
      { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入负责人名称',
          },
        },
        field: 'responsibleName',
        title: '负责人名称',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入职务',
          },
        },
        field: 'jobDesc',
        title: '职务',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入负责人电话',
          },
        },
        field: 'responsiblePhone',
        title: '负责人电话',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入负责人微信号',
          },
        },
        field: 'responsibleWechat',
        title: '负责人微信号',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入负责人邮箱',
          },
        },
        field: 'responsibleEmail',
        title: '负责人邮箱',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入备注',
          },
        },
        field: 'remark',
        title: '备注',
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            onClick: _onActionClick,
          },
          name: 'CellOperation',
          options: ['edit', 'cancel', 'delete'],
        },
        title: '操作',
        width: 200,
      },
    ],
    editRules: {
      jobDesc: [
        {
          max: 1000,
          message: '职务描述长度不超过1000',
          trigger: 'blur',
        },
      ],
      remark: [
        {
          max: 1000,
          message: '备注长度不超过1000',
          trigger: 'blur',
        },
      ],
      responsibleEmail: [
        {
          max: 500,
          message: '负责人邮箱长度不超过500',
          trigger: 'blur',
        },
      ],
      responsibleName: [
        { message: '负责人名称不能为空', required: true, trigger: 'blur' },
        {
          max: 120,
          message: '负责人名称长度不超过120',
          min: 1,
          trigger: 'blur',
        },
      ],
      responsiblePhone: [
        {
          max: 11,
          message: '长度不超过11',
          trigger: 'blur',
        },
      ],
      responsibleWechat: [
        {
          max: 100,
          message: '负责人微信号长度不超过100',
          trigger: 'blur',
        },
      ],
    },
  };
};

export const useViewGridOptions = <T = RowType>(): Partial<VxeGridProps<T>> => {
  return {
    border: true,
    columns: [
      { field: 'responsibleName', title: '联系人姓名' },
      { field: 'jobDesc', title: '职务' },
      { field: 'responsiblePhone', title: '联系电话' },
      { field: 'responsibleWechat', title: '联系人微信号' },
      { field: 'responsibleEmail', title: '联系人邮箱' },
      { field: 'remark', title: '备注' },
    ],
    pagerConfig: {
      enabled: false,
    },
  };
};
