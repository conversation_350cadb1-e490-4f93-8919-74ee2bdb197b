<script lang="ts" setup>
import { defineProps, onMounted, ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import {
  createStaff,
  getPositionDetail,
  getStaffDetail,
  updateStaff,
} from '#/api/base-info';
import FormCard from '#/components/form-card/Index.vue';

import BaseFromEdit from './basic-form/form-edit/index.vue';
import BaseFromView from './basic-form/form-view/index.vue';
import PersonnelFormEdit from './personnel-form/form-edit/index.vue';
import PersonnelFormView from './personnel-form/form-view/index.vue';

const props = defineProps({
  isView: {
    default: false,
    type: Boolean,
  },
  staffId: {
    default: '',
    type: String,
  },
});
const emits = defineEmits(['staffFormSubmitCancel', 'staffFormSubmitSuccess']);

/** 基础资料表单 */
const BaseFromRef = ref();
/** 人事资料表单 */
const PersonnelFormRef = ref();
/** 加载中 */
const loading = ref(false);
/** 确认对话框抽象*/
const confirm = (content: string) => {
  return ElMessageBox.confirm(content, '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  });
};

/** 统一提交处理*/
const onSubmit = async () => {
  const serialNumber: any =
    await PersonnelFormRef.value.formApi?.getFieldComponentRef('serialNumber');
  const isCompleted = await serialNumber?.getCompleteStatus();
  if (!isCompleted) {
    ElMessage.warning('请等待附件上传完成');
    return;
  }
  // 处理地区值
  BaseFromRef.value.formApi.setFieldValue(
    'placeOrigin',
    BaseFromRef.value.areaOptions.join(','),
  );
  // 验证表单
  const [res1, res2] = await Promise.all([
    BaseFromRef.value.formApi.validate(),
    PersonnelFormRef.value.formApi.validate(),
  ]);
  // 获取表单值
  const [baseFromValues, personnelFormValues] = await Promise.all([
    BaseFromRef.value.formApi.getValues(),
    PersonnelFormRef.value.formApi.getValues(),
  ]);
  if (!res1.valid || !res2.valid) {
    ElMessage.error('表单验证失败');
    return;
  }
  // 确认密码校验
  if (baseFromValues.isAddUser && !baseFromValues.confirmPassword) {
    ElMessage.error('确认密码校验失败');
    return;
  }
  const userId = baseFromValues?.userId?.userId;
  // 合并表单值
  const mergedValues = {
    ...baseFromValues,
    ...personnelFormValues,
    staffId: props.staffId,
    userId: userId || baseFromValues?.userId || '',
  };
  const isUpdate = !!props.staffId;
  const confirmText = isUpdate ? '确定更新吗？' : '确定提交吗？';
  if (await confirm(confirmText)) {
    try {
      loading.value = true;
      await (isUpdate ? updateStaff : createStaff)(mergedValues);
      emits('staffFormSubmitSuccess');
      ElMessage.success(isUpdate ? '编辑成功' : '添加成功');
    } catch {
      ElMessage.error('操作失败');
    } finally {
      loading.value = false;
    }
  }
};

/** 获取数据 */
const loadData = async (staffId: string) => {
  try {
    loading.value = true;
    // 获取员工详细信息
    const staffDetail = await getStaffDetail(staffId);
    let upPositionName;
    // 获取上级岗位名称
    if (staffDetail.directorPositionId) {
      // 获取当前岗位
      upPositionName = await getPositionDetail(staffDetail.directorPositionId);
    }
    // 上级岗位名称
    staffDetail.upPositionName = upPositionName?.upPositionName;
    // 地区赋值 拆分添加到数组中
    if (staffDetail.placeOrigin) {
      BaseFromRef.value.areaOptions = staffDetail.placeOrigin.split(',');
    }
    if (staffDetail.userId) {
      BaseFromRef.value.formApi.setValues({
        ...staffDetail,
        userId: {
          userId: staffDetail.userId,
          username: staffDetail.username,
        },
      });
      // 在编辑状态禁用staffCode
      BaseFromRef.value.formApi.setState((prev: any) => {
        prev.schema?.forEach((item: any) => {
          if (item.fieldName === 'staffCode' && item.componentProps) {
            item.componentProps.disabled = true;
          }
        });

        return prev;
      });
    } else {
      BaseFromRef.value.formApi.setValues({
        ...staffDetail,
      });
    }

    await PersonnelFormRef.value.formApi.setValues(staffDetail);
  } catch (error) {
    console.error(error);

    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(async () => {
  if (props.staffId) {
    loadData(props.staffId);
  }
});
defineExpose({ onSubmit });
</script>
<template>
  <div v-loading="loading">
    <FormCard title="基础资料">
      <BaseFromView v-if="isView" ref="BaseFromRef" />
      <BaseFromEdit v-else ref="BaseFromRef" />
    </FormCard>
    <FormCard title="人事资料">
      <PersonnelFormView v-if="isView" ref="PersonnelFormRef" />
      <PersonnelFormEdit v-else ref="PersonnelFormRef" />
    </FormCard>
  </div>
</template>
