import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { getDictItemList } from '#/api/common';

export function useBaseInfoFormSchema(isViewMode: boolean): VbenFormSchema[] {
  return [
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
        },
        api: () => {
          return getDictItemList('pmsCheckOptions');
        },
        clearable: true,
        placeholder: '请选择',
      },
      fieldName: isViewMode ? 'checkOptionLabel' : 'checkOption',
      label: '来料检验',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
        },
        api: () => {
          return getDictItemList('pmsWarehousingOptions');
        },
        clearable: true,
        placeholder: '请选择',
      },
      fieldName: isViewMode ? 'warehousingOptionLabel' : 'warehousingOption',
      label: '采购入库',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
        },
        api: () => {
          return getDictItemList('pmsAcceptOptions');
        },
        clearable: true,
        placeholder: '请选择',
      },
      fieldName: isViewMode ? 'acceptOptionLabel' : 'acceptOption',
      label: '采购验收',
    },
  ];
}
