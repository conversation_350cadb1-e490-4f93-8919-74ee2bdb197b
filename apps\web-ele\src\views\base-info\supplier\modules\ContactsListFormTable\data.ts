import type { OnActionClickFn, VxeGridProps } from '@girant/adapter';

/** 联系人信息表单表格 */
export interface RowType {
  contactsEmail: string;
  contactsId?: string;
  contactsName: string;
  contactsPhone: string;
  contactsWechat: string;
  customerId?: string;
  isDefault: boolean;
  jobDesc: string;
}

// 编辑表单表格
export const useGridOptions = <T = RowType>(
  _onActionClick: OnActionClickFn<T> = () => {},
): Partial<VxeGridProps<T>> => {
  return {
    columns: [
      { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入',
          },
        },
        field: 'contactsName',
        title: '联系人姓名',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入',
          },
        },
        field: 'jobDesc',
        title: '职务',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入',
          },
        },
        field: 'contactsPhone',
        title: '联系电话',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入',
          },
        },
        field: 'contactsWechat',
        title: '联系人微信号',
      },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入',
          },
        },
        field: 'contactsEmail',
        title: '联系人邮箱',
      },
      {
        cellRender: {
          attrs: {
            activeText: '是',
            inactiveText: '否',
          },
          name: 'CellSwitch',
        },
        field: 'isDefault',
        title: '是否默认联系人',
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            onClick: _onActionClick,
          },
          name: 'CellOperation',
          options: ['edit', 'cancel', 'delete'],
        },
        title: '操作',
        width: 200,
      },
    ],
    editRules: {
      contactsEmail: [
        {
          max: 500,
          message: '长度不超过500',
          trigger: 'blur',
        },
      ],
      contactsName: [
        { message: '联系人姓名不能为空', required: true, trigger: 'blur' },
        {
          max: 20,
          message: '联系人姓名长度必须在 1 到 20 个字符之间',
          min: 1,
          trigger: 'blur',
        },
      ],
      contactsPhone: [
        {
          max: 11,
          message: '长度不超过11',
          trigger: 'blur',
        },
      ],
      contactsWechat: [
        {
          max: 30,
          message: '长度不超过30',
          trigger: 'blur',
        },
      ],
      jobDesc: [
        {
          max: 1000,
          message: '长度不超过1000',
          trigger: 'blur',
        },
      ],
    },
  };
};

export const useViewGridOptions = (): VxeGridProps<RowType> => {
  return {
    border: true,
    columns: [
      { field: 'contactsName', title: '联系人姓名' },
      { field: 'jobDesc', title: '职务' },
      { field: 'contactsPhone', title: '联系电话' },
      { field: 'contactsWechat', title: '联系人微信号' },
      { field: 'contactsEmail', title: '联系人邮箱' },
      {
        field: 'isDefault',
        slots: {
          default: ({ row }) => {
            return row.isDefault ? '是' : '否';
          },
        },
        title: '是否默认联系人',
      },
    ],
    pagerConfig: {
      enabled: false,
    },
  };
};
