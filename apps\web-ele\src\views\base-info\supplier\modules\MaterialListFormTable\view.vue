<script lang="ts" setup>
import type { RowType } from './data';

import { defineProps, nextTick, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getMaterialList } from '#/api/base-info';

import { useViewGridOptions } from './data';

const props = defineProps({
  isEdit: {
    default: false,
    type: Boolean,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  supplierCode: {
    default: '',
    type: String,
  },
  supplierId: {
    default: '',
    type: String,
  },
});

const loading = ref(false);
const gridTableData = ref<Array<RowType>>([]);

// 预览表格
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions: useViewGridOptions() });

/** 获取数据 */
async function loadData(supplierId: string) {
  try {
    loading.value = true;
    const FormTableRes = await getMaterialList({
      supplierId,
    });

    if (!FormTableRes) {
      return;
    }

    gridTableData.value = FormTableRes;

    nextTick(() => {
      gridApi.setGridOptions({
        data: gridTableData.value,
      });
    });

    return gridTableData.value;
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  if (!isEmpty(props.supplierId)) {
    loadData(props.supplierId);
  }
});
</script>

<template>
  <div v-loading="loading">
    <Grid />
  </div>
</template>
