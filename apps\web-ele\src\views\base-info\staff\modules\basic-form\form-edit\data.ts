import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadPic } from '@girant-web/upload-pic-component';
import { z } from '@girant/adapter';

import { getDictItemList } from '#/api/common';
import { getUserPage } from '#/api/core';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';

const fetchUsers = async ({
  keyword,
  pageNum,
  pageSize,
}: {
  keyword: string;
  pageNum: number;
  pageSize: number;
}) => {
  return getUserPage({ username: keyword, pageNum, pageSize });
};
/** 基础资料表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '为空时系统自动生成',
      },
      dependencies: {
        rules(values: any) {
          if (values.staffCode) {
            return z.string().max(128, '员工编号长度不能超过128');
          }
          return null;
        },
        triggerFields: ['staffCode'],
      },
      fieldName: 'staffCode',
      label: '员工编号',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'staffName',
      label: '员工姓名',
      rules: z
        .string()
        .min(1, '请输入员工姓名')
        .max(20, '员工姓名长度不能超过128'),
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'contactNumber',
      label: '联系电话',
      rules: z.string().regex(/^1[3-9]\d{9}$/, {
        message: '请输入正确的联系电话',
      }),
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.email) {
            return z.string().max(500, '邮箱长度不能超过500');
          }
          return null;
        },
        triggerFields: ['email'],
      },
      fieldName: 'email',
      label: '邮箱',
    },
    {
      component: 'DatePicker',
      componentProps: {
        class: 'flex-auto',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      dependencies: {
        rules(values: any) {
          if (values.birthday) {
            return z.string().refine(
              (date) => {
                const newDate = new Date(date);
                const today = new Date();
                return newDate <= today;
              },
              {
                message: '不可选择未来日期',
              },
            );
          }
          return null;
        },
        triggerFields: ['birthday'],
      },
      fieldName: 'birthday',
      label: '出生日期',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const sexTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return sexTypeList;
        },
        api: () => {
          return getDictItemList('baseGenderType');
        },
        clearable: true,
      },
      fieldName: 'genderType',
      label: '性别',
      rules: 'selectRequired',
    },
    {
      component: 'Input',
      fieldName: 'idCard',
      label: '身份证号',
      dependencies: {
        rules(values: any) {
          if (values.idCard) {
            return z.string().regex(/(^\d{18}$)|(^\d{17}([\dX])$)/i, {
              message: '请输入正确的身份证号',
            });
          }
          return null;
        },
        triggerFields: ['idCard'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'graduationInstitution',
      label: '毕业院校',
      dependencies: {
        rules(values: any) {
          if (values.graduationInstitution) {
            return z.string().max(500, { message: '最多输入500个字符' });
          }
          return null;
        },
        triggerFields: ['graduationInstitution'],
      },
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const educationalTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return educationalTypeList;
        },
        api: () => {
          return getDictItemList('baseEducation');
        },
        clearable: true,
        filterable: true,
      },
      fieldName: 'educationalLevel',
      label: '学历',
    },
    {
      component: 'Input',
      fieldName: 'placeOrigin',
      label: '籍贯',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const ethnicGroupTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return ethnicGroupTypeList;
        },
        api: () => {
          return getDictItemList('baseEthnicGroups');
        },
        clearable: true,
        filterable: true,
      },
      fieldName: 'ethnicGroup',
      label: '民族',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const politicalTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return politicalTypeList;
        },
        api: () => {
          return getDictItemList('basePoliticalStatus');
        },
        clearable: true,
        filterable: true,
      },
      fieldName: 'politicalStatus',
      label: '政治面貌',
    },
    {
      component: 'Textarea',
      componentProps: {
        autosize: { minRows: 1 },
        maxlength: 500,
        placeholder: '请输入',
        showWordLimit: true,
      },
      fieldName: 'residentialAddress',
      formItemClass: 'col-span-full',
      label: '联系地址',
    },
    {
      component: h(RemoteSearchSelect, {
        fetchMethod: fetchUsers,
        valueKey: 'userId',
        labelKey: 'username',
        placeholder: '请输入用户名搜索',
        pageSize: 6,
      }),
      dependencies: {
        if(values) {
          return !values.isAddUser;
        },
        triggerFields: ['isAddUser'],
      },
      modelPropName: 'modelValue',
      fieldName: 'userId',
      formItemClass: 'col-span-1',
      label: '关联用户账号',
    },
    {
      component: 'Checkbox',
      fieldName: 'isAddUser',
      label: '',
      renderComponentContent: () => {
        return {
          default: () => ['新增用户账号'],
        };
      },
      dependencies: {
        trigger: (values) => {
          if (values.isAddUser) {
            values.userId = undefined;
          } else {
            values.username = undefined;
            values.password = undefined;
            values.confirmPassword = undefined;
          }
        },
        triggerFields: ['isAddUser'],
      },
      labelWidth: 30,
      formItemClass: 'col-span-1',
    },
    {
      componentProps: {
        clearable: true,
      },
      dependencies: {
        if(values) {
          return !!values.isAddUser;
        },
        triggerFields: ['isAddUser'],
      },
      component: 'Input',
      fieldName: 'username',
      formItemClass: 'col-span-1',
      label: '用户名',
      rules: z
        .string()
        .min(3, { message: '最少输入3个字符' })
        .max(64, { message: '最多输入64个字符' }),
    },
    {
      componentProps: {
        clearable: true,
        showPassword: true,
      },
      dependencies: {
        if(values) {
          return !!values.isAddUser;
        },
        triggerFields: ['isAddUser'],
      },
      component: 'Input',
      fieldName: 'password',
      formItemClass: 'col-span-1',
      rules: z
        .string()
        .min(6, { message: '最少输入6个字符' })
        .max(64, { message: '最多输入64个字符' }),
      label: '密码',
    },
    {
      componentProps: {
        clearable: true,
        showPassword: true,
      },
      dependencies: {
        if(values) {
          return !!values.isAddUser;
        },
        rules(values: any) {
          if (values.password !== values.confirmPassword) {
            return z.string().max(0, { message: '密码不一致' });
          }
          return 'required';
        },
        triggerFields: ['isAddUser', 'password', 'confirmPassword'],
      },
      component: 'Input',
      fieldName: 'confirmPassword',
      formItemClass: 'col-span-1',
      label: '确认密码',
      rules: z.string().min(1, { message: '请输入确认密码' }),
    },
    {
      component: h(UploadPic),
      modelPropName: 'imgId', // 绑定imgId进行回显
      fieldName: 'avatarId',
      formItemClass: 'col-span-full',
      label: '头像',
    },
  ];
}
