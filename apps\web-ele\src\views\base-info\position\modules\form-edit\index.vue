<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox, ElTag, ElTreeSelect } from 'element-plus';

import {
  getAllDeptTree,
  getPositionDetail,
  modPosition,
  savePosition,
} from '#/api/base-info';

import { useFormSchema } from './data';

const props = defineProps({
  /** 岗位ID */
  positionId: {
    default: '',
    type: String,
  },
});
const emits = defineEmits([
  'positionFormSubmitCancel',
  'positionFormSubmitSuccess',
]);

const loading = ref(false);
/** 部门数据 */
const deptData = ref<{ children: any; label: string; value: string }[]>([]);
/** 统一提交处理*/
const onSubmit = async (values: Record<string, any>) => {
  const serialNumber: any = await formApi?.getFieldComponentRef('serialNumber');
  const isCompleted = await serialNumber?.getCompleteStatus();
  if (!isCompleted) {
    ElMessage.warning('请等待附件上传完成');
    return;
  }
  const isUpdate = !!props.positionId;
  const confirmText = isUpdate ? '确定更新吗？' : '确定提交吗？';
  if (await confirm(confirmText)) {
    try {
      loading.value = true;
      await (isUpdate ? modPosition : savePosition)({
        ...values,
        positionId: props.positionId,
      });
      emits('positionFormSubmitSuccess');
      ElMessage.success(isUpdate ? '编辑成功' : '添加成功');
    } catch {
      ElMessage.error('操作失败');
    } finally {
      loading.value = false;
    }
  }
};
/** 添加岗位表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
  },
  handleSubmit: onSubmit,
  schema: useFormSchema(props.positionId),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

/** 确认对话框抽象*/
const confirm = (content: string) => {
  return ElMessageBox.confirm(content, '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  });
};

/** 获取数据 */
const loadData = async (positionId: string) => {
  try {
    loading.value = true;
    const data = await getPositionDetail(positionId);
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
/** 获取部门数据 */
const getDeptData = async () => {
  try {
    // 转换函数
    const convertDeptData = (dept: any) => {
      return {
        label: `【${dept.deptCode}】${dept.deptName}`,
        value: dept.deptId,
        isEnable: dept.isEnable,
        children: dept.children
          ? dept.children.map((child: any) => convertDeptData(child))
          : [],
      };
    };
    const res = await getAllDeptTree({});
    deptData.value = res.map((dept: any) => convertDeptData(dept));
  } catch {
    ElMessage.error('获取部门数据失败');
  }
};

onMounted(async () => {
  await getDeptData();
  if (props.positionId) {
    loadData(props.positionId);
  }
});
defineExpose({
  formApi,
});
</script>
<template>
  <Form v-loading="loading">
    <template #deptId="row">
      <ElTreeSelect
        v-bind="row"
        :data="deptData"
        :clearable="true"
        :check-strictly="true"
        default-expand-all
        filterable
      >
        <template #default="{ data: { label, isEnable } }">
          <div class="flex">
            <span>{{ label }}</span>
            <ElTag v-if="!isEnable" size="small" class="ml-2">停用</ElTag>
          </div>
        </template>
      </ElTreeSelect>
    </template>
  </Form>
</template>
