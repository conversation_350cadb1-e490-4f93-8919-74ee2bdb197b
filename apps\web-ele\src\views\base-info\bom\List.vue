<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';
import type { UploadRequestOptions } from 'element-plus';

import type { Recordable } from '@vben/types';

import type { BaseDataMaterialApi } from '#/api/base-info';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { downloadFileFromBlob } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElMessage,
  ElMessageBox,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import {
  BomTemplate,
  disableProdBom,
  enableProdBom,
  exportMaterial,
  getProdBomPage,
  importProdBom,
} from '#/api/base-info';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

/** 物料id */
const materialId = ref('');
/** 物料编号 */
const materialCode = ref('');
const isView = ref(false);
const modalFormRef = ref();
/** 当前查看的BOMID */
const bomId = ref('');

const currentBomData = ref<any>({});

/** 创建时间 */
const createTime = ref({
  // 开始时间
  startCreateTime: '',
  // 结束时间
  endCreateTime: '',
});

/** 操作 */
const onActionClick = (
  e: OnActionClickParams<BaseDataMaterialApi.BaseDataMaterial>,
) => {
  switch (e.code) {
    case 'edit': {
      onEdit(e.row);
      break;
    }
    case 'view': {
      onView(e.row);
      break;
    }
  }
};

const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onBeforeClose: () => {
    materialId.value = '';
    materialCode.value = '';
    isView.value = false;
    bomId.value = '';
    return true;
  },
  destroyOnClose: true,
  onConfirm: () => {
    modalFormRef.value.submitAllForm();
  },
  showCancelButton: true,
  showConfirmButton: true,
  closeOnClickModal: false,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      createTime.value = {
        startCreateTime: '',
        endCreateTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[70px]',
    },
    schema: useGridFormSchema(),
    showCollapseButton: false,
    wrapperClass:
      'grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-6',
  },
  gridOptions: {
    border: true,
    cellConfig: {
      height: 60,
    },
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.startCreateTime = createTime.value.startCreateTime;
          params.endCreateTime = createTime.value.endCreateTime;

          return await getProdBomPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'materialId',
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<BaseDataMaterialApi.BaseDataMaterial>,
});

/** 打开模态框 */
function openFormModal(title: string, showConfirmButton = true) {
  formModalApi
    .setState({
      showConfirmButton,
      title,
    })
    .open();
}

function onEditForBomId(data: any) {
  formModalApi.close();

  setTimeout(() => {
    onEdit(data);
  }, 250);
}

/** 编辑 */
function onEdit(row: any) {
  materialId.value = row.materialId;
  materialCode.value = row.materialCode;
  bomId.value = row.bomId;
  isView.value = false;
  openFormModal('修改BOM信息', true);
}

/** 新增 */
function onCreate() {
  materialId.value = '';
  materialCode.value = '';
  bomId.value = '';
  isView.value = false;
  openFormModal('新增BOM信息', true);
}

/** 查看 */
async function onView(row: BaseDataMaterialApi.BaseDataMaterial) {
  materialId.value = row.materialId;
  materialCode.value = row.materialCode;
  bomId.value = row.bomId;
  isView.value = true;
  openFormModal('查看BOM信息', false);
}

const setFormModalLoading = (load: boolean) => {
  formModalApi.setState({ loading: load });
};

/** 关闭表单 */
const setFormModalClose = (state: boolean = false) => {
  if (state) {
    formModalApi.close();
  }
  gridApi.query();
};

/** 下载模板 */
async function getBomTemplate() {
  try {
    const blob = await BomTemplate();
    downloadFileFromBlob({
      source: blob,
      fileName: 'BOM模板',
    });
  } catch {
    ElMessage.error('文件下载失败：');
  }
}

/** 数据导入*/
async function importProdBomHandle(options: UploadRequestOptions) {
  const { file, onSuccess } = options;
  try {
    const data = { file };
    const response = await importProdBom(data);
    gridApi.query();
    onSuccess(response);
    ElMessage.success('数据导入成功');
  } catch {
    ElMessage.error('数据导入失败');
  }
}

/** 数据导出 */
async function exportMaterialHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    const blob = await exportMaterial(formValues);
    downloadFileFromBlob({
      source: blob,
      fileName: 'BOM管理列表',
    });
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}

/** 切换可用状态 */
async function onStatusChange(newStatus: Boolean, row: any) {
  const isValid: Recordable<string> = {
    false: $t('common.disabled'),
    true: $t('common.enabled'),
  };
  try {
    await ElMessageBox.confirm(
      `您要将 【${row.materialName}】 的状态切换为 【${isValid[newStatus.toString()]}】 吗？`,
      '提示',
      {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      },
    );

    await (row.isValid ? disableProdBom(row.bomId) : enableProdBom(row.bomId));

    gridApi.query();

    if (currentBomData.value) {
      modalFormRef.value.loadData();
      modalFormRef.value.loadHistoryListFormTable();
    }

    return true;
  } catch {
    return false;
  }
}

function onLoadData(data: any) {
  currentBomData.value = data;
}

const createDisabledDate = (isEnd: boolean) => {
  return (time: Date) => {
    if (!createTime.value.endCreateTime && !isEnd) {
      return false;
    }
    // 是结束时间
    return isEnd
      ? time.getTime() < new Date(createTime.value.startCreateTime).getTime()
      : time.getTime() > new Date(createTime.value.endCreateTime).getTime();
  };
};
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-8/12">
      <Form
        ref="modalFormRef"
        :material-id="materialId"
        :bom-id="bomId"
        :material-code="materialCode"
        @is-submit="setFormModalLoading"
        @submit-state="setFormModalClose"
        :is-view="isView"
        @edit-for-bom-id="onEditForBomId"
        @load-data="onLoadData"
      />

      <template #center-footer v-if="isView">
        <ElButton
          v-access:code="'base:bom:enable:enable'"
          type="success"
          v-if="currentBomData.isNew && !currentBomData.isValid"
          @click="onStatusChange(true, currentBomData)"
        >
          启用
        </ElButton>

        <ElButton
          v-access:code="'base:bom:enable:disable'"
          type="danger"
          v-if="currentBomData.isNew && currentBomData.isValid"
          @click="onStatusChange(false, currentBomData)"
        >
          停用
        </ElButton>
        <ElButton
          v-access:code="'base:bom:edit:add'"
          type="primary"
          @click="
            onEditForBomId({
              bomId,
              materialId,
              materialCode,
            })
          "
        >
          发起变更
        </ElButton>
      </template>
    </FormModal>
    <Grid>
      <template #form-createTime>
        <ElDatePicker
          v-model="createTime.startCreateTime"
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false)"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="createTime.endCreateTime"
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true)"
          class="!w-full"
        />
      </template>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onCreate"
          v-access:code="'base:bom:edit:add'"
        >
          新增BOM信息
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="下载导入模板"
          placement="top-start"
        >
          <ElButton circle @click="getBomTemplate" class="mr-2">
            <template #icon><span class="iconfont">&#xe678;</span></template>
          </ElButton>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导入数据"
          placement="top-start"
        >
          <ElUpload
            :show-file-list="false"
            :http-request="importProdBomHandle"
            accept=".xlsx,.xls"
          >
            <ElButton circle class="mr-2" v-access:code="'base:bom:import'">
              <template #icon>
                <span class="iconfont">&#xe621;</span>
              </template>
            </ElButton>
          </ElUpload>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportMaterialHandle"
            v-access:code="'base:bom:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
