import type { OnActionClickFn, VxeGridProps } from '@girant/adapter';

/** 客户标签信息表单表格 */
export interface RowType {
  tagDesc: string;
  tagId?: string;
  tagName: string;
}

// 编辑表单表格
export const useGridOptions = <T = RowType>(
  _onActionClick: OnActionClickFn<T> = () => {},
): Partial<VxeGridProps<T>> => {
  return {
    columns: [
      { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
      {
        editRender: {
          name: 'CellInput',
          props: {
            placeholder: '请输入标签名称',
          },
        },
        field: 'tagName',
        title: '标签名称',
      },
      {
        editRender: {
          attrs: {},
          name: 'CellInput',
          props: {
            placeholder: '请输入说明',
          },
        },
        field: 'tagDesc',
        title: '说明',
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            onClick: _onActionClick,
          },
          name: 'CellOperation',
          options: ['edit', 'cancel', 'delete'],
        },
        title: '操作',
        width: 200,
      },
    ],
    editRules: {
      tagDesc: [
        {
          max: 1000,
          message: '说明长度不超过1000',
          trigger: 'blur',
        },
      ],
      tagName: [
        { message: '标签名称不能为空', required: true, trigger: 'blur' },
        {
          max: 20,
          message: '标签名称长度必须在 1 到 20 个字符之间',
          min: 1,
          trigger: 'blur',
        },
      ],
    },
  };
};

export const useViewGridOptions = <T = RowType>(): Partial<VxeGridProps<T>> => {
  return {
    border: true,
    columns: [
      { field: 'tagName', title: '标签名称' },
      { field: 'tagDesc', title: '说明' },
    ],
    pagerConfig: {
      enabled: false,
    },
  };
};
