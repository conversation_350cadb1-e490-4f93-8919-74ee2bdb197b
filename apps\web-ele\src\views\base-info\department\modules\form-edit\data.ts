import type { VbenFormSchema } from '@girant/adapter';

import type { BaseDataDeptApi } from '#/api/base-info';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { z } from '@girant/adapter';

import { getAllDeptTree } from '#/api/base-info';

/** 表单 */
export function useFormSchema(deptId: string): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        disabled: !!deptId,
        placeholder: '为空时系统自动生成',
      },
      dependencies: {
        rules(values: any) {
          if (values.deptCode) {
            return z.string().max(128, '部门编号长度不能超过128');
          }
          return null;
        },
        triggerFields: ['deptCode'],
      },
      fieldName: 'deptCode',
      label: '部门编号',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'deptName',
      label: '部门名称',
      rules: z
        .string()
        .min(1, '请输入部门名称')
        .max(100, '部门名称长度不能超过100'),
    },
    {
      component: 'ApiTreeSelect',
      componentProps: {
        afterFetch: (data: BaseDataDeptApi.deptTreeType[]) => {
          // 转换函数 //isDisabled是否禁用当前节点包括子节点
          const convertDeptData = (dept: any, isDisabled: boolean = false) => {
            return {
              disabled: isDisabled,
              label: `【${dept.deptCode}】${dept.deptName}`,
              value: dept.deptId,
              children: dept.children
                ? dept.children.map((child: BaseDataDeptApi.deptTreeType) =>
                    // 禁用当前节点的子部门
                    convertDeptData(
                      child,
                      isDisabled ? true : child.deptId === deptId,
                    ),
                  )
                : [],
            };
          };
          // 执行转换
          const convertedData = data.map((dept) =>
            convertDeptData(dept, dept.deptId === deptId),
          );
          return convertedData;
        },
        api: () => {
          return getAllDeptTree({ isEnable: true });
        },
        checkStrictly: true,
        clearable: true,
        filterable: true,
      },
      fieldName: 'parentId',
      label: '上级部门',
    },
    {
      component: 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
      fieldName: 'deptDescribe',
      formItemClass: 'col-span-full items-start',
      label: '部门介绍',
      rules: z
        .string()
        .min(1, '请输入部门介绍')
        .max(1000, '最多输入1000个字符'),
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
    },
  ];
}
