import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { UploadFiles } from '@girant-web/upload-files-component';
import { UploadPic } from '@girant-web/upload-pic-component';
import { z } from '@girant/adapter';

import { getDictItemList } from '#/api/common';
import { getUserPage } from '#/api/core';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';

const fetchUsers = async ({
  keyword,
  pageNum,
  pageSize,
}: {
  keyword: string;
  pageNum: number;
  pageSize: number;
}) => {
  return getUserPage({
    username: keyword,
    isEnable: true,
    userType: '21',
    pageNum,
    pageSize,
  });
};

export function useBaseInfoFormSchema(isViewMode: boolean): VbenFormSchema[] {
  return [
    {
      component: isViewMode
        ? (props: any) => {
            return h('div', null, props.modelValue);
          }
        : 'Input',
      componentProps: {
        clearable: true,
        disabled: true,
        placeholder: '系统默认自动生成',
      },
      dependencies: {
        rules(values: any) {
          if (values.customerCode) {
            return z.string().max(128, { message: '最多输入128个字符' });
          }
          return null;
        },
        triggerFields: ['customerCode'],
      },
      fieldName: 'customerCode',
      label: '客户编号',
    },
    {
      component: isViewMode
        ? (props: any) => {
            return h('div', null, props.modelValue);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'customerName',
      label: '客户名称',
      rules: z
        .string()
        .min(1, '请输入客户名称')
        .max(100, '客户名称长度不能超过100'),
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.customerEnName) {
            return z.string().max(100, { message: '客户英文名长度不超过100' });
          }
          return null;
        },
        triggerFields: ['customerEnName'],
      },
      fieldName: 'customerEnName',
      label: '客户英文名',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.customerAbbr) {
            return z.string().max(20, { message: '客户简称长度不超过20' });
          }
          return null;
        },
        triggerFields: ['customerAbbr'],
      },
      fieldName: 'customerAbbr',
      label: '客户简称',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.contactsPhone) {
            return z
              .string()
              .max(11, { message: '联系电话长度不超过11' })
              .regex(/^1[3-9]\d{9}$/, {
                message: '请输入正确的手机号码',
              });
          }
          return null;
        },
        triggerFields: ['contactsPhone'],
      },
      fieldName: 'contactsPhone',
      label: '联系电话',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.address) {
            return z.string().max(1000, { message: '地址信息长度不超过1000' });
          }
          return null;
        },
        triggerFields: ['address'],
      },
      fieldName: 'address',
      formItemClass: 'col-span-full',
      label: '地址信息',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
        },
        api: () => {
          return getDictItemList('baseCompanyScale');
        },
        clearable: true,
        placeholder: '请选择企业规模',
      },
      fieldName: isViewMode ? 'companyScaleLabel' : 'companyScale',
      label: '企业规模',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
        },
        api: () => {
          return getDictItemList('baseCompanyType');
        },
        clearable: true,
        placeholder: '请选择企业性质',
      },
      fieldName: isViewMode ? 'companyTypeLabel' : 'companyType',
      label: '企业性质',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : h(RemoteSearchSelect, {
            fetchMethod: fetchUsers,
            valueKey: 'userId',
            labelKey: 'username',
            placeholder: '请输入用户名搜索',
            pageSize: 6,
          }),
      modelPropName: 'modelValue',
      dependencies: {
        if(values) {
          return !values.isAddUser;
        },
        triggerFields: ['isAddUser'],
      },
      fieldName: isViewMode ? 'username' : 'userInfo',
      formItemClass: 'col-start-1',
      label: '关联用户账号',
    },

    {
      component: 'Checkbox',
      fieldName: 'isAddUser',
      label: '',
      renderComponentContent: () => {
        return {
          default: () => ['新增用户账号'],
        };
      },
      dependencies: {
        if: !isViewMode,
        trigger: (values) => {
          if (values.isAddUser) {
            values.userId = undefined;
          } else {
            values.username = undefined;
            values.password = undefined;
            values.confirmPassword = undefined;
          }
        },
        triggerFields: ['isAddUser'],
      },
    },

    {
      componentProps: {
        clearable: true,
      },
      dependencies: {
        if(values) {
          return !!values.isAddUser;
        },
        triggerFields: ['isAddUser'],
      },
      component: 'Input',
      fieldName: 'username',
      formItemClass: 'col-span-1',
      label: '用户名',
      rules: z
        .string()
        .min(3, { message: '最少输入3个字符' })
        .max(64, { message: '最多输入64个字符' }),
    },
    {
      componentProps: {
        clearable: true,
        showPassword: true,
      },
      dependencies: {
        if(values) {
          return !!values.isAddUser;
        },
        triggerFields: ['isAddUser'],
      },
      component: 'Input',
      fieldName: 'password',
      formItemClass: 'col-span-1',
      rules: z
        .string()
        .min(6, { message: '最少输入6个字符' })
        .max(64, { message: '最多输入64个字符' }),
      label: '密码',
    },
    {
      componentProps: {
        clearable: true,
        showPassword: true,
      },
      dependencies: {
        if(values) {
          return !!values.isAddUser;
        },
        rules(values: any) {
          if (values.password !== values.confirmPassword) {
            return z.string().max(0, { message: '密码不一致' });
          }
          return 'required';
        },
        triggerFields: ['isAddUser', 'password', 'confirmPassword'],
      },
      component: 'Input',
      fieldName: 'confirmPassword',
      formItemClass: 'col-span-1',
      label: '确认密码',
      rules: z.string().min(1, { message: '请输入确认密码' }),
    },

    {
      component: h(UploadFiles, {
        mode: isViewMode ? 'readMode' : 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'licenseSerialNumber',
      formItemClass: 'col-span-full items-start',
      label: '营业执照',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.remark) {
            return z.string().max(1000, { message: '备注长度不超过1000' });
          }
          return null;
        },
        triggerFields: ['remark'],
      },
      fieldName: 'remark',
      formItemClass: 'col-span-full items-start',
      label: '备注',
    },

    {
      component: (props: any) => {
        return isViewMode
          ? h(ImageViewer, {
              imgId: props.modelValue,
              imgCss: 'size-40',
              imgFit: 'cover',
              class: '!w-[160px]',
            })
          : h(UploadPic, {
              imgId: props.modelValue,
              allowedFormats: ['jpg'],
              textConfig: {
                tipText: '只支持 .jpg 格式',
              },
            });
      },
      modelPropName: 'imgId',
      fieldName: 'pictureFileId',
      formItemClass: 'col-span-full col-start-1 items-start',
      label: '图片',
    },

    {
      component: h(UploadFiles, {
        mode: isViewMode ? 'readMode' : 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
    },
  ];
}
