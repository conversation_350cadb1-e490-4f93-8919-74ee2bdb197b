import { computed, defineComponent, ref } from 'vue';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import Edit from './edit.vue';
import View from './view.vue';

const NumberingRule = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    View,
  },
  name: 'NumberingRule',
  props: {
    formConfig: {
      default: () => ({}),
      type: Object,
    },
    isEdit: {
      default: false,
      type: Boolean,
    },
    isView: {
      default: false,
      type: Boolean,
    },
    materialCode: {
      default: '',
      type: String,
    },
    materialId: {
      default: '',
      type: String,
    },
  },
  setup(props, { expose }) {
    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();

    const getNumberingRuleValues = async () => {
      if (componentRef.value?.onSubmitToNumberingRule) {
        return await componentRef.value.onSubmitToNumberingRule();
      }
      return null;
    };

    const loadData = (data: any) => {
      componentRef.value.loadData(data);
    };

    expose({
      getNumberingRuleValues,
      loadData,
    });

    return {
      componentRef,
      currentComponent,
    };
  },
  template: `
    <FormCard title="编号规则">
      <component
        ref="componentRef"
        :is="currentComponent"
        :materialCode="materialCode"
        :materialId="materialId"
        :formConfig="formConfig"
        :isView="isView"
        :isEdit="isEdit"
      />
    </FormCard>
  `,
});

export { NumberingRule };
