<script lang="ts" setup>
import { defineProps, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getSupplierInfo } from '#/api/base-info';

import { useBaseInfoFormSchema } from './data';

const props = defineProps({
  formConfig: {
    default: () => {},
    type: Object,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  supplierCode: {
    default: '',
    type: String,
  },
  supplierId: {
    default: '',
    type: String,
  },
});

const loading = ref(false);

/** 基础资料表单 */
const [Form, BaseInfoFormApi] = useVbenForm({
  ...props.formConfig,
  schema: useBaseInfoFormSchema(props.isView),
});

/** 获取数据 */
async function loadData(supplierCode: string, supplierId: string) {
  try {
    loading.value = true;
    const BaseInfoFormRes = await getSupplierInfo({
      supplierCode,
      supplierId,
    });

    BaseInfoFormApi.setValues(BaseInfoFormRes);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  if (!isEmpty(props.supplierId) || !isEmpty(props.supplierCode)) {
    loadData(props.supplierCode, props.supplierId);
  }
});
</script>

<template>
  <Form v-loading="loading" />
</template>
