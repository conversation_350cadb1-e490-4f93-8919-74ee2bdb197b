import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { BaseDataMaterialApi } from '#/api/base-info';

import { h, markRaw } from 'vue';

import { useAccess } from '@vben/access';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElInputTag, ElTag } from 'element-plus';

import { getDictItemList } from '#/api/common';

const { hasAccessByCodes } = useAccess();

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'materialCodeList',
      formItemClass: 'col-span-2',
      label: '物料编号',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'materialName',
      label: '物料名称',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const res = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return res;
        },
        api: () => {
          return getDictItemList('baseMaterialAttribute');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'materialAttributeList',
      label: '物料属性',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const res = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return res;
        },
        api: () => {
          return getDictItemList('baseMaterialType');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'materialTypeList',
      label: '物料大类',
    },

    {
      component: 'Input',
      fieldName: 'materialCategoryList',
      label: '物料细类',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '标准', value: true },
          { label: '非标准', value: false },
        ],
      },
      fieldName: 'isStandard',
      label: '标准状态',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '正常', value: true },
          { label: '停用', value: false },
        ],
      },
      fieldName: 'isEnable',
      label: '使用状态',
    },
  ];
}

/** 表格 */
export function useColumns<T = BaseDataMaterialApi.BaseDataMaterial>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  const operationColumn = {
    align: 'center',
    cellRender: {
      attrs: {
        nameField: 'customerName',
        nameTitle: '物料管理',
        onClick: onActionClick,
      },
      name: 'CellOperation',
      options: [
        ...(hasAccessByCodes(['base:material:query:detail']) ? ['view'] : []),
        ...(hasAccessByCodes(['base:material:edit:modify']) ? ['edit'] : []),
      ],
    },
    field: 'operation',
    fixed: 'right',
    title: '操作',
    width: 150,
  } as NonNullable<VxeTableGridOptions['columns']>[0];

  return [
    {
      type: 'checkbox',
      width: 30,
      // slots: { checkbox: 'checkbox_cell' },
    },
    { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
    {
      field: 'pictureFileId',
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
          }),
      },
      title: '图片',
      width: 100,
    },

    {
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'relative',
            },
            [
              h('span', row.materialCode),
              row.isStandard
                ? null
                : h(
                    ElTag,
                    {
                      type: 'danger',
                      class: 'ml-2 absolute top-[-1px] right-0',
                    },
                    { default: () => '非标' },
                  ),
            ],
          );
        },
      },
      field: 'materialCode',
      title: '物料编号',
      minWidth: 220,
    },

    {
      field: 'materialName',
      minWidth: 200,
      title: '物料名称',
    },

    {
      field: 'materialAlias',
      minWidth: 120,
      title: '物料别名',
    },
    {
      field: 'materialSpecs',
      minWidth: 200,
      title: '型号规格',
    },
    {
      field: 'materialAttributeLabel',
      title: '物料属性',
      width: 100,
    },
    {
      field: 'materialTypeLabel',
      title: '物料大类',
      width: 100,
    },

    {
      field: 'materialCategoryName',
      title: '物料细类',
      width: 100,
    },
    {
      field: 'baseUnitLabel',
      title: '基本单位',
      width: 100,
    },

    {
      field: 'remark',
      title: '备注',
      width: 200,
    },

    {
      field: 'isDeprecated',
      slots: {
        default: 'isDeprecated',
      },
      title: '推荐状态',
      width: 100,
    },

    {
      field: 'isStandard',
      title: '标准状态',
      visible: false,
    },

    {
      cellRender: {
        attrs: {
          activeText: '已启用',
          beforeChange: onStatusChange,
          inactiveText: '已禁用',
        },
        name: hasAccessByCodes([
          'base:material:enable:enable',
          'base:material:enable:disable',
        ])
          ? 'CellSwitch'
          : 'CellTag',
      },
      field: 'isEnable',
      title: '启用状态',
      width: 100,
    },
    ...(hasAccessByCodes([
      'base:material:query:detail',
      'base:material:edit:modify',
    ])
      ? [operationColumn]
      : []),
  ];
}
