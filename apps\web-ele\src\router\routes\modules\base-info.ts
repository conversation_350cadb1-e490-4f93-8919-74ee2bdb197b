import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      order: 2,
      title: '基础资料',
    },
    name: 'BaseInfo',
    path: '/base-info',
    children: [
      {
        component: () => import('#/views/base-info/staff/List.vue'),
        meta: {
          title: '员工管理',
        },
        name: 'staff',
        path: '/base-info/staff',
      },
      {
        component: () => import('#/views/base-info/position/List.vue'),
        meta: {
          title: '岗位管理',
        },
        name: 'position',
        path: '/base-info/position',
      },
      {
        component: () => import('#/views/base-info/customer/List.vue'),
        meta: {
          title: '客户管理',
        },
        name: 'customer',
        path: '/base-info/customer',
      },
      {
        component: () => import('#/views/base-info/supplier/List.vue'),
        meta: {
          title: '供应商管理',
        },
        name: 'supplier',
        path: '/base-info/supplier',
      },
      {
        component: () => import('#/views/base-info/department/List.vue'),
        meta: {
          title: '部门管理',
        },
        name: 'department',
        path: '/base-info/department',
      },
      {
        component: () => import('#/views/base-info/material/List.vue'),
        meta: {
          title: '物料管理',
        },
        name: 'material',
        path: '/base-info/material',
      },
      {
        component: () => import('#/views/base-info/bom/List.vue'),
        meta: {
          title: 'BOM管理',
        },
        name: 'bom',
        path: '/base-info/bom',
      },
    ],
  },
];

export default routes;
