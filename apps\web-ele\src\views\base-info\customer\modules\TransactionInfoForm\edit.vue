<script lang="ts" setup>
import { defineProps, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import {
  getCustomerTransaction,
  updateCustomerTransaction,
} from '#/api/base-info';
import { isObjectEmpty } from '#/utils/data-processing-utils';

import { useTransactionInfoFormSchema } from './data';

const props = defineProps({
  customerCode: {
    default: '',
    type: String,
  },
  customerId: {
    default: '',
    type: String,
  },
  formConfig: {
    default: () => {},
    type: Object,
  },
  isEdit: {
    default: false,
    type: Boolean,
  },
  isView: {
    default: false,
    type: Boolean,
  },
});

const loading = ref(false);

/** 表单 */
const [Form, FormApi] = useVbenForm({
  ...props.formConfig,
  schema: useTransactionInfoFormSchema(props.isView),
});

const getFormValues = async () => {
  const formValues = await FormApi.getValues();
  return formValues;
};

const validateForm = async () => {
  const validatRes = await FormApi.validate();
  return validatRes;
};

// 统一提交处理
const onSubmitToForm = async () => {
  try {
    loading.value = true;
    const mergedValues = await getFormValues();
    FormApi.resetValidate();
    // 空表单检查 - 仅在新建模式下
    if (!props.isEdit && isObjectEmpty(mergedValues)) {
      return null;
    }

    // 表单验证
    const { valid } = await validateForm();
    if (!valid) {
      if (props.isEdit) {
        // 编辑模式：显示错误消息
        ElMessage.error('请正确填写表单');
        return null;
      } else {
        // 新建模式：抛出错误给调用方
        throw new Error('交易信息表单验证失败');
      }
    }

    // 编辑模式
    if (props.isEdit) {
      mergedValues.customerId = props.customerId;
      if (
        await ElMessageBox.confirm('确定修改吗？', '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning',
        })
      ) {
        await updateCustomerTransaction(mergedValues);
        ElMessage.success('编辑成功');
      }
      return null;
    }

    // 新建模式
    return mergedValues;
  } catch (error) {
    if (!props.isEdit && error instanceof Error) {
      // 新建模式下，将错误继续向上抛出
      throw error;
    } else {
      // 编辑模式错误处理
      ElMessage.error('编辑失败');
      console.error(error);
      return null;
    }
  } finally {
    loading.value = false;
  }
};

// 表单数据
const formValue = ref();

/** 获取数据 */
async function loadData(customerId: string) {
  try {
    loading.value = true;
    const InfoFormRes = await getCustomerTransaction({
      customerId,
    });

    if (!InfoFormRes) {
      return;
    }

    formValue.value = InfoFormRes;

    const deliveryMethodList = InfoFormRes.deliveryMethodList;

    InfoFormRes.deliveryMethodList = deliveryMethodList
      ? deliveryMethodList.map((item: any) => {
          return item.deliveryMethod;
        })
      : [];

    FormApi.setValues(InfoFormRes);

    return InfoFormRes;
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}
onMounted(async () => {
  if (!isEmpty(props.customerId)) {
    loadData(props.customerId);
  }
});

defineExpose({
  Form,
  FormApi,
  onSubmitToForm,
});
</script>

<template>
  <Form v-loading="loading" />
</template>
