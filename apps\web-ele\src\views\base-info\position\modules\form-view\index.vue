<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getAllRoleList, getPositionDetail } from '#/api/base-info';

import { useFormSchema } from './data';

const props = defineProps({
  /** 岗位ID */
  positionId: {
    default: '',
    type: String,
  },
});

const loading = ref(false);
const roleList = ref([]);

/** 岗位表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
  },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

/** 获取数据 */
const loadData = async (positionId: string) => {
  try {
    loading.value = true;
    roleList.value = await getAllRoleList();
    const data = await getPositionDetail(positionId);
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(async () => {
  loadData(props.positionId);
});
defineExpose({
  formApi,
});
</script>
<template>
  <Form v-loading="loading">
    <template #roleIdList="row">
      <div>
        {{
          roleList
            .filter((post: any) => row.value?.includes(post.roleId))
            .map((post: any) => post.roleName)
            .join('、')
        }}
      </div>
    </template>
  </Form>
</template>
