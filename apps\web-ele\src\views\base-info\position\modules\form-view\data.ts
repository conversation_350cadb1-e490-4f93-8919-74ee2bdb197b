import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

import { getAllRoleList } from '#/api/base-info';

/** 表单 查看*/
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'positionCode',
      label: '岗位编号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'positionName',
      label: '岗位名称',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'positionTypeLabel',
      formItemClass: 'col-span-full',
      label: '岗位类型',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'deptName',
      label: '所属部门',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'upPositionName',
      label: '上级岗位',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'positionDuty',
      formItemClass: 'col-span-full items-start',
      label: '岗位职责',
    },
    {
      component: 'ApiCheckboxGroup',
      componentProps: {
        afterFetch: (data: any) => {
          const roleList = data.map((item: any) => ({
            label: item.roleName,
            value: item.roleId,
          }));
          return roleList;
        },
        api: () => {
          return getAllRoleList();
        },
        class: 'flex flex-wrap',
      },
      fieldName: 'roleIdList',
      formItemClass: 'col-span-full items-start',
      label: '关联角色',
    },
    {
      component: h(UploadFiles, {
        mode: 'readMode',
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      wrapperClass: 'mt-[2px]',
      label: '附件',
    },
  ];
}
