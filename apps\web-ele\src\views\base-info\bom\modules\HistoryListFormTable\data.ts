import type { VxeTableGridOptions } from '@girant/adapter';

/** 表单表格 */
// export interface RowType {
//   baseUnitLabel: string;
//   materialAttributeLabel: string;
//   materialId: string;
//   materialName: string;
//   materialSpecs: string;
//   materialTypeLabel: string;
//   usageQuantity: string;
// }

// 编辑表单表格
export function useGridOptions(): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
    { field: 'bomId', title: 'BOMID', visible: false },
    {
      field: 'currentVersion',
      title: '版本号',
      slots: {
        default: 'currentVersion',
      },
      align: 'left',
      minWidth: 180,
    },
    {
      field: 'isValid',
      title: '状态',
      width: 120,
      slots: {
        default: 'isValid',
      },
    },
    { field: 'createUserName', title: '创建人', minWidth: 150 },
    { field: 'createTime', title: '创建时间', width: 180 },
    {
      align: 'right',
      slots: {
        default: 'operation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 240,
      headerAlign: 'center',
    },
    {
      field: 'historyVersionId',
      title: '历史版本ID',
      visible: false,
    },
  ];
}
