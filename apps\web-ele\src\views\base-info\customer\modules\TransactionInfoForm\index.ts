import { computed, defineComponent, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import Edit from './edit.vue';
import View from './view.vue';

const TransactionInfoForm = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    View,
  },
  name: 'TransactionInfoForm',
  props: {
    customerCode: {
      default: '',
      type: String,
    },
    customerId: {
      default: '',
      type: String,
    },
    formConfig: {
      default: () => ({}),
      type: Object,
    },
    isView: {
      default: false,
      type: Boolean,
    },
  },
  setup(props, { expose }) {
    const isEdit = computed(() => !isEmpty(props.customerId));

    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();

    const getTransactionInfoFormValues = async () => {
      if (componentRef.value?.onSubmitToForm) {
        return await componentRef.value.onSubmitToForm();
      }
      return null;
    };

    const handleEdit = async () => {
      componentRef.value.onSubmitToForm();
    };

    expose({
      getTransactionInfoFormValues,
    });

    return {
      componentRef,
      currentComponent,
      handleEdit,
      isEdit,
    };
  },
  template: `
    <FormCard title="交易信息">
      <template #titleMore v-if="isEdit && !isView">
        <ElButton type="primary" @click="handleEdit" size="small">修改</ElButton>
      </template>
      <template #default>
        <component
          ref="componentRef"
          :is="currentComponent"
          :customerCode="customerCode"
          :customerId="customerId"
          :formConfig="formConfig"
          :isView="isView"
          :isEdit="isEdit"
        />
      </template>
    </FormCard>
  `,
});

export { TransactionInfoForm };
