import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { BaseDataPositionApi } from '#/api/base-info';

import { h, markRaw } from 'vue';

import { useAccess } from '@vben/access';

import { ElInputTag, ElTag } from 'element-plus';

import { getDictItemList } from '#/api';
import { getAllDeptTree, getPositionList } from '#/api/base-info';

const { hasAccessByCodes } = useAccess();
/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'positionCodeList',
      label: '岗位编号',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'positionName',
      label: '岗位名称',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const PositionTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return PositionTypeList;
        },
        api: () => {
          return getDictItemList('basePositionType');
        },
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        maxCollapseTags: 1,
      },
      defaultValue: [],
      fieldName: 'positionTypeList',
      label: '岗位类型',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '全部', value: '' },
          { label: '正常', value: true },
          { label: '停用', value: false },
        ],
      },
      defaultValue: true,
      fieldName: 'isEnable',
      label: '岗位状态',
    },
    {
      component: 'ApiTreeSelect',
      componentProps: {
        afterFetch: (data: any) => {
          // 转换函数
          function convertDeptData(dept: any) {
            return {
              label: `【${dept.deptCode}】${dept.deptName}`,
              value: dept.deptId,
              children: dept.children
                ? dept.children.map((child: any) => convertDeptData(child))
                : [],
            };
          }
          // 执行转换
          const convertedData = data.map((dept: any) => convertDeptData(dept));
          return convertedData;
        },
        api: () => {
          return getAllDeptTree({});
        },
        checkStrictly: true,
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        filterable: true,
        maxCollapseTags: 2,
        multiple: true,
      },
      fieldName: 'deptIdList',
      formItemClass: 'col-span-2',
      label: '所属部门',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        filterable: true,
        maxCollapseTags: 3,
        multiple: true,
        placeholder: '选择岗位',
        afterFetch: (data: any) => {
          const res = data.map((item: any) => ({
            label: item.positionName,
            value: item.positionId,
            deptName: item.deptName,
            isEnable: item.isEnable,
          }));
          return res;
        },
        api: () => {
          return getPositionList({});
        },
      },
      renderComponentContent: () => ({
        default: ({ item }: { item: any }) => {
          return h(
            'div',
            {
              class: 'flex justify-between',
            },
            [
              h(
                'span',
                {},
                {
                  default: () => item.label,
                },
              ),
              h(
                'span',
                {
                  size: 'small',
                  class: 'ml-2 text-neutral-400',
                },
                {
                  default: () => item.deptName,
                },
              ),
            ],
          );
        },
      }),
      fieldName: 'parentId',
      formItemClass: 'col-span-2',
      label: '上级岗位',
    },
  ];
}

/** 部门树 岗位表格 */
export function useTreeColumns<T = BaseDataPositionApi.BaseDataPosition>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  const operationColumn = {
    align: 'center',
    cellRender: {
      attrs: {
        nameField: 'positionName',
        nameTitle: '岗位设置',
        onClick: onActionClick,
      },
      name: 'CellOperation',
      options: [
        ...(hasAccessByCodes(['base:position:query:detail']) ? ['view'] : []),
        ...(hasAccessByCodes(['base:position:edit:modify']) ? ['edit'] : []),
      ],
    },
    field: 'operation',
    fixed: 'right',
    minWidth: 150,
    title: '操作',
  } as NonNullable<VxeTableGridOptions['columns']>[0];
  return [
    {
      field: 'dept',
      title: '部门',
      width: 200,
      align: 'left',
      headerAlign: 'center',
      treeNode: true,
    },
    {
      field: 'positionName',
      title: '岗位名称',
      width: 200,
    },
    {
      field: 'positionCode',
      minWidth: 150,
      title: '岗位编号',
    },
    {
      field: 'positionTypeLabel',
      slots: {
        default: ({ row }) => {
          return h(
            ElTag,
            {
              type: 'primary',
            },
            {
              default: () => row.positionTypeLabel || '未知',
            },
          );
        },
      },
      title: '岗位类型',
      width: 100,
    },
    {
      field: 'positionDuty',
      title: '岗位职责',
      minWidth: 200,
    },
    {
      field: 'upPositionName',
      slots: {
        default: 'upPositionName',
      },
      align: 'left',
      headerAlign: 'center',
      title: '上级岗位',
      width: 250,
    },
    {
      cellRender: {
        attrs: {
          activeText: '已启用',
          beforeChange: onStatusChange,
          inactiveText: '已禁用',
        },
        name: hasAccessByCodes([
          'base:position:enable:enable',
          'base:position:enable:disable',
        ])
          ? 'CellSwitch'
          : 'CellTag',
      },
      field: 'isEnable',
      title: '启用状态',
      width: 100,
    },
    ...(hasAccessByCodes([
      'base:position:query:detail',
      'base:position:edit:modify',
    ])
      ? [operationColumn]
      : []),
  ];
}
