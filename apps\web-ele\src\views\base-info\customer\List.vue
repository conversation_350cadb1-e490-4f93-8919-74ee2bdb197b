<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';
import type { UploadRequestOptions } from 'element-plus';

import type { Recordable } from '@vben/types';

import type { BaseDataCustomerApi } from '#/api/base-info';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { downloadFileFromBlob } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElMessage,
  ElMessageBox,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import {
  customerExport,
  customerImport,
  customerTemplate,
  disableCustomer,
  enableCustomer,
  getCustomerPageList,
} from '#/api/base-info';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

/** 员工id */
const customerId = ref('');
/** 员工编号 */
const customerCode = ref('');

// 是否是查看状态
const isView = ref(false);
const modalFormRef = ref();

const [FormModal, formModalApi] = useVbenModal({
  confirmText: '提交',
  onBeforeClose: () => {
    customerId.value = '';
    customerCode.value = '';
    // isView.value = false;
    return true;
  },
  onConfirm: () => {
    modalFormRef.value.submitAllForm();
  },
  showCancelButton: true,
  closeOnClickModal: false,
});

/** 切换客户可用状态 */
async function onStatusChange(
  newStatus: Boolean,
  row: BaseDataCustomerApi.BaseDataCustomer,
) {
  const isEnable: Recordable<string> = {
    false: $t('common.disabled'),
    true: $t('common.enabled'),
  };
  try {
    await ElMessageBox.confirm(
      `您要将 【${row.customerName}】 的状态切换为 【${isEnable[newStatus.toString()]}】 吗？`,
      '提示',
      {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
      },
    );

    if (row.isEnable) {
      await disableCustomer(row.customerId);
      return true;
    } else {
      await enableCustomer(row.customerId);
      return true;
    }
  } catch {
    return false;
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[70px]',
    },
    schema: useGridFormSchema(),
    showCollapseButton: false,
    wrapperClass:
      'grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          if (formValues.companyScaleList.length > 0) {
            formValues.companyScaleList = formValues.companyScaleList.join(',');
          }

          if (formValues.companyTypeList.length > 0) {
            formValues.companyTypeList = formValues.companyTypeList.join(',');
          }
          return await getCustomerPageList({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      height: 60,
      keyField: 'customerId',
    },
    // showOverflow: false,
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<BaseDataCustomerApi.BaseDataCustomer>,
});

function onActionClick(
  e: OnActionClickParams<BaseDataCustomerApi.BaseDataCustomer>,
) {
  switch (e.code) {
    case 'edit': {
      onEdit(e.row);
      break;
    }
    case 'view': {
      onView(e.row);
      break;
    }
  }
}

/** 编辑 */
function onEdit(row: BaseDataCustomerApi.BaseDataCustomer) {
  customerId.value = row.customerId;
  customerCode.value = row.customerCode;
  isView.value = false;
  formModalApi
    .setState({
      showConfirmButton: false,
      title: `${$t('common.edit')}客户信息`,
    })
    .open();
}
/** 新增 */
function onCreate() {
  customerId.value = '';
  customerCode.value = '';
  isView.value = false;
  formModalApi
    .setState({
      showConfirmButton: true,
      title: `${$t('common.create')}客户信息`,
    })
    .open();
}
/** 查看 */
async function onView(row: BaseDataCustomerApi.BaseDataCustomer) {
  customerId.value = row.customerId;
  customerCode.value = row.customerCode;
  isView.value = true;
  formModalApi
    .setState({
      showConfirmButton: false,
      title: '查看客户信息',
    })
    .open();
}

const setFormModalLoading = (load: boolean) => {
  formModalApi.setState({ loading: load });
};

const setFormModalClose = (state: boolean = false) => {
  if (state) {
    formModalApi.close();
  }
  gridApi.query();
};

async function getCustomerTemplate() {
  try {
    const blob = await customerTemplate();
    downloadFileFromBlob({
      source: blob,
      fileName: '客户模板',
    });
  } catch (error) {
    console.error('文件下载失败：', error);
  }
}

async function customerImportHandle(options: UploadRequestOptions) {
  const { file, onSuccess } = options;
  try {
    const data = { file };
    const response = await customerImport(data);
    gridApi.query();

    onSuccess(response);
    ElMessage.success('数据导入成功');
  } catch {
    ElMessage.error('数据导入失败');
  }
}

async function customerExportHandle() {
  let exportData: {
    address?: string;
    companyScaleList?: string;
    companyTypeList?: string;
    customerCodeList?: string;
    customerName?: string;
    isEnable?: boolean;
    tagName?: string;
  } = {};

  // 获取选择的客户编号
  const grid = gridApi.grid;
  const checkedData = grid.getCheckboxRecords();

  if (checkedData.length > 0) {
    exportData.customerCodeList = checkedData
      .map((item) => item.customerCode)
      .join(',');
  } else {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    exportData = { ...formValues };
  }

  try {
    const blob = await customerExport(exportData);
    downloadFileFromBlob({
      source: blob,
      fileName: '客户管理列表',
    });
    ElMessage.success('数据导出成功');
  } catch (error) {
    console.error('数据导出失败:', error);
    ElMessage.error('数据导出失败');
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-8/12">
      <Form
        ref="modalFormRef"
        :customer-id="customerId"
        :customer-code="customerCode"
        :is-view="isView"
        @is-submit="setFormModalLoading"
        @submit-state="setFormModalClose"
      />
    </FormModal>
    <Grid>
      <template #tagList="{ row }">
        <template v-for="tag in row.tagList" :key="tag.tagId">
          <ElTooltip
            class="box-item"
            effect="dark"
            :content="tag.tagDesc || '暂无说明'"
            placement="top-start"
          >
            <el-tag class="ml-2 mt-1">
              {{ tag.tagName }}
            </el-tag>
          </ElTooltip>
        </template>
      </template>
      <template #toolbar-actions>
        <ElButton
          v-access:code="'base:customer:edit:add'"
          type="primary"
          @click="onCreate"
        >
          {{ `${$t('common.create')}客户信息` }}
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="下载导入模板"
          placement="top-start"
        >
          <ElButton circle @click="getCustomerTemplate" class="mr-2">
            <template #icon><span class="iconfont">&#xe678;</span></template>
          </ElButton>
        </ElTooltip>

        <ElTooltip
          class="box-item"
          effect="light"
          content="导入数据"
          placement="top-start"
        >
          <ElUpload
            :show-file-list="false"
            :http-request="customerImportHandle"
            accept=".xlsx,.xls"
          >
            <ElButton
              circle
              class="mr-2"
              v-access:code="'base:customer:import'"
            >
              <template #icon>
                <span class="iconfont">&#xe621;</span>
              </template>
            </ElButton>
          </ElUpload>
        </ElTooltip>

        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="customerExportHandle"
            v-access:code="'base:customer:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
