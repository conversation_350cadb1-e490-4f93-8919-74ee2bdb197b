import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { useAccess } from '@vben/access';

import { ImageViewer } from '@girant-web/img-view-component';
import { UploadFiles } from '@girant-web/upload-files-component';
import { UploadPic } from '@girant-web/upload-pic-component';
import { z } from '@girant/adapter';

import { getDictItemList } from '#/api';
import { getMaterialCategoryTree } from '#/api/base-info';

const { hasAccessByCodes } = useAccess();

export function useBaseInfoFormSchema(
  isViewMode: boolean,
  materialId?: string,
): VbenFormSchema[] {
  return [
    {
      component: isViewMode
        ? (props: any) => {
            return h('div', null, props.modelValue);
          }
        : 'Input',
      componentProps: {
        disabled: true,
        placeholder: '系统默认自动生成',
      },
      fieldName: 'materialCode',
      label: '物料编号',
    },
    {
      component: isViewMode
        ? (props: any) => {
            return h('div', null, props.modelValue);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'materialName',
      label: '物料名称',
      rules: z
        .string()
        .min(1, '请输入物料名称')
        .max(50, '物料名称长度不能超过50'),
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.materialAlias) {
            return z.string().max(50, { message: '物料别名长度不能超过50' });
          }
          return null;
        },
        triggerFields: ['materialAlias'],
      },
      fieldName: 'materialAlias',
      label: '物料别名',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.materialSpecs) {
            return z.string().max(50, { message: '物料规格长度不能超过50' });
          }
          return null;
        },
        triggerFields: ['materialSpecs'],
      },
      fieldName: 'materialSpecs',
      formItemClass: 'col-span-full',
      label: '物料规格',
      rules: 'required',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
        },
        api: () => {
          return getDictItemList('baseMaterialUnit');
        },
        clearable: true,
        placeholder: '请选择',
      },
      fieldName: isViewMode ? 'baseUnitLabel' : 'baseUnit',
      label: '基本单位',
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
        },
        api: () => {
          return getDictItemList('baseMaterialQuantityDecimal');
        },
        clearable: true,
        placeholder: '请选择',
      },
      fieldName: isViewMode ? 'quantityDecimalLabel' : 'quantityDecimal',
      label: '数量精度',
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Select',
      componentProps: {
        options: [
          ...(hasAccessByCodes(['base:material:standard:edit:add'])
            ? [{ label: '是', value: true }]
            : []),
          ...(hasAccessByCodes(['base:material:nonstandard:edit:add'])
            ? [{ label: '否', value: false }]
            : []),
        ],
      },
      defaultValue: '',
      dependencies: {
        if: !materialId,
        triggerFields: [''],
      },
      fieldName: 'isStandard',
      formItemClass: 'col-start-1',
      label: '标准物料',
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            return h('div', null, val ? '不推荐' : '推荐');
          }
        : 'Select',
      componentProps: {
        options: [
          { label: '推荐', value: false },
          { label: '不推荐', value: true },
        ],
      },
      defaultValue: false,
      fieldName: 'isDeprecated',
      label: '是否不推荐使用',
      rules: 'selectRequired',
      labelWidth: 110,
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: [], // 初始为空
      },
      defaultValue: '',
      dependencies: {
        async componentProps() {
          const data = await getDictItemList('baseMaterialAttribute');
          return {
            options: data.map((item: any) => ({
              label: item.dictLabel,
              value: item.dictValue,
            })),
          };
        },
        triggerFields: [''],
      },
      fieldName: isViewMode ? 'materialAttributeLabel' : 'materialAttribute',
      formItemClass: 'col-start-1 col-span-2',
      label: '物料属性',
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiTreeSelect',
      componentProps: {
        afterFetch: (data: any) => {
          // 转换函数
          function convertDeptData(data: any) {
            return {
              label: data.categoryName,
              value: data.categoryCode,
              children: data.children
                ? data.children.map((child: any) => convertDeptData(child))
                : [],
            };
          }
          // 执行转换
          const convertedData = data.map((data: any) => convertDeptData(data));
          return convertedData;
        },
        api: () => {
          return getMaterialCategoryTree();
        },
      },
      fieldName: 'materialCategory',
      formItemClass: 'col-start-1',
      label: '物料细类',
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: [], // 初始为空
      },
      defaultValue: '',
      dependencies: {
        async componentProps() {
          const data = await getDictItemList('baseMaterialType');
          return {
            options: data.map((item: any) => ({
              label: item.dictLabel,
              value: item.dictValue,
            })),
          };
        },
        triggerFields: [''],
      },
      fieldName: isViewMode ? 'materialTypeLabel' : 'materialType',
      formItemClass: 'col-span-2',
      label: '物料大类',
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.remark) {
            return z.string().max(256, { message: '备注长度不能超过256' });
          }
          return null;
        },
        triggerFields: ['remark'],
      },
      fieldName: 'remark',
      formItemClass: 'col-span-full items-start',
      label: '备注',
    },

    {
      component: (props: any) => {
        return isViewMode
          ? h(ImageViewer, {
              imgId: props.modelValue,
              imgCss: 'size-40',
              imgFit: 'cover',
              class: '!w-[160px]',
            })
          : h(UploadPic, {
              imgId: props.modelValue,
              allowedFormats: ['jpg'],
              textConfig: {
                tipText: '只支持 .jpg 格式',
              },
            });
      },
      modelPropName: 'imgId',
      fieldName: 'pictureFileId',
      formItemClass: 'col-span-full col-start-1 items-start',
      label: '图片',
      // rules: 'required',
    },

    {
      component: h(UploadFiles, {
        mode: isViewMode ? 'readMode' : 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
      // rules: 'required',
    },
  ];
}
