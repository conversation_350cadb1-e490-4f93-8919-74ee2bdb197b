<script lang="ts" setup>
import { defineProps, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import { getCustomerBaseInfo, updateCustomerBaseInfo } from '#/api/base-info';

import { useBaseInfoFormSchema } from './data';

const props = defineProps({
  customerCode: {
    default: '',
    type: String,
  },
  customerId: {
    default: '',
    type: String,
  },
  formConfig: {
    default: () => {},
    type: Object,
  },
  isEdit: {
    default: false,
    type: Boolean,
  },
  isView: {
    default: false,
    type: Boolean,
  },
});

const emits = defineEmits(['submitState']);

const loading = ref(false);

/** 基础资料表单 */
const [Form, BaseInfoFormApi] = useVbenForm({
  ...props.formConfig,
  schema: useBaseInfoFormSchema(props.isView),
});

const getFormValues = async () => {
  const formValues = await BaseInfoFormApi.getValues();
  return formValues;
};

const validateForm = async () => {
  const validatRes = await BaseInfoFormApi.validate();
  return validatRes;
};

// 统一提交处理
const onSubmitToBaseInfoForm = async () => {
  const { valid } = await validateForm();
  if (!valid) {
    if (props.isEdit) {
      ElMessage.error('请正确填写表单');
      return null;
    } else {
      throw new Error('基础信息表单验证失败');
    }
  }

  const serialNumber: any =
    await BaseInfoFormApi?.getFieldComponentRef('serialNumber');
  const isCompleted_SerialNumber = await serialNumber.getCompleteStatus();
  if (!isCompleted_SerialNumber) {
    ElMessage.error('等待附件上传完成');
    return;
  }

  const licenseSerialNumber: any = await BaseInfoFormApi?.getFieldComponentRef(
    'licenseSerialNumber',
  );
  const isCompleted_LicenseSerialNumber =
    await licenseSerialNumber.getCompleteStatus();
  if (!isCompleted_LicenseSerialNumber) {
    ElMessage.error('等待营业执照上传完成');
    return;
  }

  const mergedValues = await getFormValues();

  if (mergedValues.isAddUser) {
    delete mergedValues.userInfo;
    delete mergedValues.userId;
  } else {
    mergedValues.userId = mergedValues?.userInfo?.userId || '';
    delete mergedValues.userInfo;
    delete mergedValues.username;
  }

  if (props.isEdit) {
    mergedValues.customerId = props.customerId;
  }

  switch (props.isEdit) {
    case false: {
      return mergedValues;
    }
    case true: {
      if (
        await ElMessageBox.confirm('确定修改吗？', '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning',
        })
      ) {
        try {
          loading.value = true;
          await updateCustomerBaseInfo(mergedValues);
          emits('submitState');
          ElMessage.success('编辑成功');
        } catch {
          ElMessage.error('编辑失败');
        } finally {
          loading.value = false;
        }
      }
      break;
    }
  }
};

// 表单数据
const formValue = ref();

/** 获取数据 */
async function loadData(customerCode: string, customerId: string) {
  try {
    loading.value = true;
    const BaseInfoFormRes = await getCustomerBaseInfo({
      customerCode,
      customerId,
    });

    formValue.value = BaseInfoFormRes;

    BaseInfoFormApi.setValues(BaseInfoFormRes);

    if (BaseInfoFormRes.userId) {
      BaseInfoFormRes.userInfo = {
        userId: BaseInfoFormRes.userId,
        username: BaseInfoFormRes.username,
      };
    }

    return BaseInfoFormRes;
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}
onMounted(async () => {
  if (!isEmpty(props.customerId) || !isEmpty(props.customerCode)) {
    loadData(props.customerCode, props.customerId);
  }
});

defineExpose({
  BaseInfoFormApi,
  Form,
  onSubmitToBaseInfoForm,
});
</script>

<template>
  <Form v-loading="loading" />
</template>
