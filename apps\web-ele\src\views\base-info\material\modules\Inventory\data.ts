import type { VbenFormSchema } from '@girant/adapter';

import { h, ref } from 'vue';

import { getWarehouseList } from '#/api/base-info';

const warehouseListHasLocationList = ref<
  Array<{
    [key: string]: any;
  }>
>([]);

export function useBaseInfoFormSchema(isViewMode: boolean): VbenFormSchema[] {
  return [
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiSelect',
      componentProps: (value: any, formApi: any) => {
        return {
          afterFetch: (data: any) => {
            warehouseListHasLocationList.value = data;
            return data.map((item: any) => ({
              label: item.warehouseName,
              value: item.warehouseId,
            }));
          },
          api: () => {
            return getWarehouseList({ isLoc: true });
          },
          placeholder: '请选择',
          clearable: true,
          onChange: (onChangeValue: string) => {
            if (onChangeValue) {
              formApi.setFieldValue('defaultLocationId', '');
            }
          },
        };
      },
      fieldName: isViewMode ? 'warehouseName' : 'defaultWarehouseId',
      label: '默认仓库',
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Select',
      componentProps: {
        options: [],
        clearable: true,
      },
      dependencies: {
        componentProps: (value: any) => {
          let options = [];

          if (value.defaultWarehouseId) {
            // formApi.setFieldValue('defaultLocationId', '');
            const warehouseHasLocationLis =
              warehouseListHasLocationList.value.find(
                (item) => item.warehouseId === value.defaultWarehouseId,
              ) || {};

            if (warehouseHasLocationLis?.locationList.length > 0) {
              options = warehouseHasLocationLis.locationList.map(
                (item: any) => {
                  return { value: item.locationId, label: item.locationName };
                },
              );
            }
          }

          return {
            options,
            disabled: !value.defaultWarehouseId,
          };
        },
        triggerFields: ['defaultWarehouseId'],
      },
      fieldName: isViewMode ? 'locationName' : 'defaultLocationId',
      label: '默认库位',
      rules: 'selectRequired',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, `${showText} 元`);
          }
        : 'InputNumber',
      componentProps: {
        placeholder: '请输入',
        controlsPosition: 'right',
        precision: 2,
      },
      fieldName: 'referenceCost',
      label: '参考成本价格',
      suffix: () =>
        isViewMode ? '' : h('span', { class: 'text-nowrap' }, '元'),
      formItemClass: 'col-start-1',
    },
  ];
}
