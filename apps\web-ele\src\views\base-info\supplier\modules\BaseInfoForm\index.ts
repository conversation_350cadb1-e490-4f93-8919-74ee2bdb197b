import { computed, defineComponent, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import Edit from './edit.vue';
import View from './view.vue';

const BaseInfoForm = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    View,
  },
  emits: ['baseInfoSubmitState'],
  name: 'BaseInfoForm',
  props: {
    formConfig: {
      default: () => ({}),
      type: Object,
    },
    isView: {
      default: false,
      type: Boolean,
    },
    supplierCode: {
      default: '',
      type: String,
    },
    supplierId: {
      default: '',
      type: String,
    },
  },
  setup(props, { emit, expose }) {
    const isEdit = computed(() => !isEmpty(props.supplierId));
    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();

    const getBaseInfoFormValues = async () => {
      if (componentRef.value?.onSubmitToBaseInfoForm) {
        return await componentRef.value.onSubmitToBaseInfoForm();
      }
      return null;
    };

    const handleEdit = async () => {
      componentRef.value.onSubmitToBaseInfoForm();
    };

    const submitState = () => {
      emit('baseInfoSubmitState');
    };

    expose({
      getBaseInfoFormValues,
    });

    return {
      componentRef,
      currentComponent,
      handleEdit,
      isEdit,
      submitState,
    };
  },
  template: `
    <FormCard title="基本信息">
      <template #titleMore v-if="isEdit && !isView">
        <ElButton type="primary" @click="handleEdit" size="small">修改</ElButton>
      </template>
      <template #default>
        <component
          ref="componentRef"
          :is="currentComponent"
          :supplierCode="supplierCode"
          :supplierId="supplierId"
          :formConfig="formConfig"
          :isView="isView"
          :isEdit="isEdit"
          @submitState="submitState"
        />
      </template>
    </FormCard>
  `,
});

export { BaseInfoForm };
