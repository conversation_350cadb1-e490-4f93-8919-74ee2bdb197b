import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath } from '../path';

export namespace BaseDataDeptApi {
  export interface BaseDataDept {
    [key: string]: any;
  }
  export interface deptTreeType {
    /** 子部门 */
    children?: deptTreeType[];
    /** 部门编号 */
    deptCode: string;
    /** 部门id */
    deptId: string;
    /** 部门名称 */
    deptName: string;
    /** 是否启用 */
    isEnable: boolean;
    /** 岗位列表 */
    positionList?: {
      /** 岗位编码 */
      positionCode: string;
      /** 岗位id */
      positionId: string;
      /** 岗位名称 */
      positionName: string;
    }[];
    /** 员工信息列表 */
    staffList?: {
      /** 员工编号 */
      staffCode: string;
      /** 员工id */
      staffId: number;
      /** 员工姓名 */
      staffName: string;
    }[];
  }
}

/** 新增部门信息 */
export async function saveDept(
  data: Omit<BaseDataDeptApi.BaseDataDept, 'deptId'>,
) {
  return requestClient.post(`${baseDataPath}/base/dept/saveDept`, data);
}

/** 修改部门信息 */
export async function modDept(
  data: Omit<BaseDataDeptApi.BaseDataDept, 'deptId'>,
) {
  return requestClient.post(`${baseDataPath}/base/dept/modDept`, data);
}
/** 导入部门信息 */
export async function importDept(data: { file: Blob | File }) {
  return requestClient.upload(`${baseDataPath}/base/dept/importDept`, data);
}
/**  查询部门树 */
export async function getDeptTree(data: any) {
  return requestClient.post<BaseDataDeptApi.deptTreeType[]>(
    `${baseDataPath}/base/dept/getDeptTree`,
    data,
  );
}
/**  查询所有部门树 */
export async function getAllDeptTree(data: any) {
  return requestClient.post<BaseDataDeptApi.deptTreeType[]>(
    `${baseDataPath}/base/dept/getAllDeptTree`,
    data,
  );
}
/** 导出部门信息 */
export async function exportDept(params: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/dept/exportDept`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 停用部门 */
export async function disableDept(deptId: string, isForceDisable?: boolean) {
  return requestClient.get(`${baseDataPath}/base/dept/disableDept`, {
    params: {
      deptId,
      isForceDisable,
    },
    responseReturn: 'body',
  });
}

/** 启用部门 */
export async function enableDept(deptId: string) {
  return requestClient.get(`${baseDataPath}/base/dept/enableDept/${deptId}`);
}

/** 根据部门id或编号获取部门详细信息 */
export async function getDeptDetail(data: any) {
  return requestClient.post(`${baseDataPath}/base/dept/getDeptDetail`, {
    ...data,
  });
}

/** 根据部门id或编号获取部门基本信息 */
export async function getDeptBaseInfo(deptId?: string, deptCode?: string) {
  return requestClient.get(`${baseDataPath}/base/dept/getDept`, {
    params: {
      deptCode,
      deptId,
    },
  });
}

/** 导出模板 */
export async function DeptTemplate() {
  return requestClient.get(`${baseDataPath}/base/dept/DeptTemplate`, {
    responseReturn: 'raw',
    responseType: 'blob', // 设置响应类型为 blob
  });
}
