import { computed, defineComponent, ref } from 'vue';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import Edit from './edit.vue';
import View from './view.vue';

const ChildrenListFormTable = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    View,
  },
  name: 'ChildrenListFormTable',
  props: {
    isView: {
      default: false,
      type: Boolean,
    },

    materialCode: {
      default: '',
      type: String,
    },
    materialId: {
      default: '',
      type: String,
    },
    isEdit: {
      default: false,
      type: Boolean,
    },
  },
  setup(props, { expose }) {
    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();

    const getChildrenListFormTableValues = async () => {
      if (componentRef.value?.onSubmitToFormTable) {
        return await componentRef.value.onSubmitToFormTable();
      }
      return null;
    };

    const handleEdit = async () => {
      componentRef.value.onSubmitToFormTable();
    };

    const loadData = (data: any) => {
      if (data.children && data.children.length > 0) {
        const tableData = data.children.map((item: any) => ({
          ...item,
          materialId: {
            materialId: item.materialId,
            materialName: item.materialName,
          },
        }));
        componentRef.value.loadData(tableData);
      }
    };

    expose({
      getChildrenListFormTableValues,
      loadData,
    });

    return {
      componentRef,
      currentComponent,
      handleEdit,
    };
  },
  template: `
    <FormCard title="子料清单">
     <template #default>
        <component
          ref="componentRef"
          :is="currentComponent"
          :materialCode="materialCode"
          :materialId="materialId"
          :isView="isView"
          :is-edit="isEdit"
        />
      </template>
    </FormCard>
  `,
});

export { ChildrenListFormTable };
