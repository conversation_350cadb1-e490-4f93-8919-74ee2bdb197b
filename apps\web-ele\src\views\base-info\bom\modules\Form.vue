<script lang="ts" setup>
import { computed, defineProps, onMounted, ref } from 'vue';

import { AccessControl } from '@vben/access';
import { isEmpty } from '@vben/utils';

import { ElMessage, ElMessageBox } from 'element-plus';

import { getProdBomDetailById, saveProdBom } from '#/api/base-info';

import { BaseInfoForm } from './BaseInfoForm/index';
import { ChildrenListFormTable } from './ChildrenListFormTable/index';
import HistoryListFormTable from './HistoryListFormTable/index.vue';

const props = defineProps({
  isView: {
    default: false,
    type: Boolean,
  },
  materialCode: {
    default: '',
    type: String,
  },
  materialId: {
    default: '',
    type: String,
  },
  bomId: {
    default: '',
    type: String,
  },
});
const emits = defineEmits([
  'isSubmit',
  'submitState',
  'editForBomId',
  'loadData',
]);

const loading = ref(false);

const currentBomId = ref(props.bomId);

const isEdit = computed(() => {
  return !isEmpty(props.materialId);
});

/** 合并公共表单配置*/
const formConfig = {
  commonConfig: {
    componentProps: { class: 'w-full' },
    hideRequiredMark: props.isView,
  },

  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
};

// 定义组件名称的字面量类型，确保类型安全
type ComponentName = 'BaseInfoForm' | 'ChildrenListFormTable';

// 组件引用映射，统一管理所有子组件
interface ComponentRefMap {
  [key: string]: {
    methods: {
      getValue: string; // 获取值的方法名
      setValue: string; // 设置值的方法名
    };
    ref: any;
  };
}

/** 物料信息 */
const BaseInfoFormRef = ref();

const ChildrenListFormTableRef = ref();

const HistoryListFormTableRef = ref();

// 组件引用和方法名映射
const componentRefs: ComponentRefMap = {
  BaseInfoForm: {
    methods: {
      getValue: 'getBaseInfoFormValues',
      setValue: 'loadData',
    },
    ref: BaseInfoFormRef,
  },

  ChildrenListFormTable: {
    methods: {
      getValue: 'getChildrenListFormTableValues',
      setValue: 'loadData',
    },
    ref: ChildrenListFormTableRef,
  },
};

/** 确认对话框抽象*/
function confirm(content: string) {
  return ElMessageBox.confirm(content, '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  });
}

/**
 * 调用组件方法
 * @param componentName 组件名称
 * @param methodType 方法类型 ('getValue' | 'setValue')
 * @param data 传递给方法的数据（用于setValue）
 * @returns 方法执行结果
 */
const callComponentMethod = async (
  componentName: ComponentName,
  methodType: 'getValue' | 'setValue',
  data?: any,
) => {
  const component = componentRefs[componentName];
  if (!component) {
    throw new Error(`组件 ${componentName} 不存在`);
  }

  const methodName = component.methods[methodType];
  const refValue = component.ref.value;

  if (!refValue || typeof refValue[methodName] !== 'function') {
    throw new Error(`组件 ${componentName} 没有 ${methodName} 方法`);
  }

  // 根据方法类型决定是否需要传递参数
  if (methodType === 'setValue' && data !== undefined) {
    return await refValue[methodName](data);
  }

  return await refValue[methodName]();
};

/**
 * 获取所有组件的值
 * @returns 合并后的表单数据
 */
const getAllComponentValues = async () => {
  const componentNames = Object.keys(componentRefs);
  const promises: Promise<any>[] = [];
  let lastPromise = Promise.resolve();
  const result: Record<string, any> = {};

  for (const name of componentNames) {
    lastPromise = lastPromise.then(async () => {
      const value = await callComponentMethod(
        name as ComponentName,
        'getValue',
      );
      if (name === 'BaseInfoForm') {
        Object.assign(result, value);
      } else if (name === 'ChildrenListFormTable') {
        result.children = value.children;
      }
    });
    promises.push(lastPromise);
  }

  await Promise.all(promises);
  return result;
};

const submitAllForm = async () => {
  const isStandard = BaseInfoFormRef.value?.materialForBomViewRef.isStandard;

  if (
    !(await confirm(
      isStandard
        ? '确定提交吗？'
        : '当前物料为非标件，提交成功后，母件将变为标准母件，是否确认提交？',
    ))
  ) {
    return;
  }

  try {
    emits('isSubmit', true);
    // 获取所有组件的值
    const formData = await getAllComponentValues();
    await saveProdBom(formData);
    if (isEmpty(props.materialId)) {
      ElMessage.success('提交成功');
    } else {
      ElMessage.success('修改成功');
    }
    emits('isSubmit', false);
    emits('submitState', true);
  } catch (error: unknown) {
    emits('isSubmit', false);
    emits('submitState', false);

    if (error instanceof Error) {
      ElMessage.error(error.message);
    }
  }
};

/**
 * 设置所有组件的值
 * @param data 要设置的数据
 */
const setAllComponentValues = (data: any) => {
  Object.keys(componentRefs).forEach((name) => {
    callComponentMethod(name as ComponentName, 'setValue', data);
  });
};

const onChangeViewBomId = (bomId: string) => {
  currentBomId.value = bomId;
  loadData();
};

const onEditForBomId = (bomId: string) => {
  currentBomId.value = bomId;
  loadData();
  emits('editForBomId', {
    bomId,
    materialId: props.materialId,
    materialCode: props.materialCode,
  });
};

const loadHistoryListFormTable = () => {
  HistoryListFormTableRef.value.loadHistoryListFormTable();
};

const loadData = async () => {
  try {
    loading.value = true;
    const allData = await getProdBomDetailById({
      bomId: currentBomId.value,
    });

    emits('loadData', allData);
    setAllComponentValues(allData);
    loading.value = false;
  } catch {
    loading.value = false;
  }
};

onMounted(async () => {
  if (!isEmpty(props.materialId)) {
    loadData();
  }
});

defineExpose({ submitAllForm, loadData, loadHistoryListFormTable });
</script>

<template>
  <div v-loading="loading">
    <BaseInfoForm
      ref="BaseInfoFormRef"
      :is-view="isView"
      :form-config="formConfig"
      :material-id="materialId"
      :material-code="materialCode"
      :is-edit="isEdit"
    />

    <ChildrenListFormTable
      ref="ChildrenListFormTableRef"
      :is-view="isView"
      :form-config="formConfig"
      :material-id="materialId"
      :material-code="materialCode"
      :is-edit="isEdit"
    />

    <AccessControl :codes="['base:bom:query:history']" type="code">
      <HistoryListFormTable
        ref="HistoryListFormTableRef"
        :is-view="isView"
        :form-config="formConfig"
        :material-id="materialId"
        :material-code="materialCode"
        :current-bom-id="currentBomId"
        v-if="isView && materialId"
        @change-view-bom-id="onChangeViewBomId"
        @edit-for-bom-id="onEditForBomId"
      />
    </AccessControl>
  </div>
</template>
