<script lang="ts" setup>
import { computed, defineProps, ref, watch } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElTag } from 'element-plus';

import { getMaterialDetail } from '#/api/base-info';

import { useMaterialForBomFormSchema } from './data';

const props = defineProps({
  currentMaterialId: {
    default: '',
    type: String,
  },
  formConfig: {
    default: () => {},
    type: Object,
  },
  isView: {
    default: false,
    type: Boolean,
  },
});

const loading = ref(false);
const materialDetail = ref<any>({});

const isStandard = computed(() => {
  return materialDetail.value ? materialDetail.value.isStandard : false;
});

/** 表单 */
const [Form, FormApi] = useVbenForm({
  ...props.formConfig,
  schema: useMaterialForBomFormSchema(),
  wrapperClass: 'grid-cols-4',
});

watch(
  () => props.currentMaterialId,
  async (nValue) => {
    if (nValue) {
      loading.value = true;
      const MaterialDetailRes = await getMaterialDetail({
        materialId: props.currentMaterialId,
      });
      materialDetail.value = MaterialDetailRes;
      FormApi.setValues(MaterialDetailRes);

      loading.value = false;
    }
  },
  {
    immediate: true,
  },
);

defineExpose({
  isStandard,
});
</script>

<template>
  <div v-loading="loading">
    <Form>
      <template #materialCode="{ modelValue }">
        {{ modelValue }}
        <ElTag class="ml-2" type="danger" v-if="!isStandard"> 非标 </ElTag>
      </template>
    </Form>
  </div>
</template>
