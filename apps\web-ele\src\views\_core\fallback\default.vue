<script lang="ts" setup>
import { computed } from 'vue';

import { Page } from '@vben/common-ui';
import { preferences } from '@vben/preferences';

const title = computed(() => preferences.login.title);
</script>

<template>
  <Page class="h-full bg-white">
    <div class="flex flex-col items-center justify-center">
      <h1
        class="mb-20 mt-28 text-center text-[clamp(2rem,5vw,3.5rem)] font-bold text-gray-800"
      >
        欢迎进入{{ title }}
      </h1>
      <el-image
        src="/static/default-01.png"
        alt="系统欢迎图片"
        class="w-3/5"
        fit="contain"
      />
    </div>
  </Page>
</template>

<style scoped>
/* 如需自定义样式可在此添加 */
</style>
