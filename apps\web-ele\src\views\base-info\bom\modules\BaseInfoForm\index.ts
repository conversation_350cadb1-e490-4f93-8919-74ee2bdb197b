import { computed, defineComponent, ref } from 'vue';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import MaterialForBom from '../MaterialForBom/index.vue';
import Edit from './edit.vue';
import View from './view.vue';

const BaseInfoForm = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    MaterialForBom,
    MaterialForBomView: MaterialForBom,
    View,
  },
  emits: ['baseInfoSubmitState'],
  name: 'BaseInfoForm',
  props: {
    formConfig: {
      default: () => ({}),
      type: Object,
    },
    isView: {
      default: false,
      type: Boolean,
    },
    materialCode: {
      default: '',
      type: String,
    },
    materialId: {
      default: '',
      type: String,
    },
    isEdit: {
      default: false,
      type: Boolean,
    },
  },
  setup(props, { expose }) {
    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();
    const MaterialForBomView = MaterialForBom;
    const materialForBomViewRef = ref<InstanceType<typeof MaterialForBom>>();

    const getBaseInfoFormValues = async () => {
      if (componentRef.value?.onSubmitToBaseInfoForm) {
        return await componentRef.value.onSubmitToBaseInfoForm();
      }
      return null;
    };

    const loadData = (data: any) => {
      const formData = {
        ...data,
        materialId: {
          materialId: data.materialId,
          materialName: data.materialName,
        },
      };
      componentRef.value.loadData(formData);
      selMaterialId(props.materialId);
    };

    const currentMaterialId = ref('');
    const selMaterialId = (materialId: string) => {
      currentMaterialId.value = materialId;
    };

    if (props.materialId) {
      currentMaterialId.value = props.materialId;
    }

    expose({
      getBaseInfoFormValues,
      loadData,
      materialForBomViewRef,
    });

    return {
      componentRef,
      currentComponent,
      currentMaterialId,
      MaterialForBomView,
      selMaterialId,
      materialForBomViewRef,
    };
  },
  template: `
    <FormCard title="物料信息">
     <template #default>
        <component
          ref="componentRef"
          :is="currentComponent"
          :materialCode="materialCode"
          :materialId="materialId"
          :formConfig="formConfig"
          :isView="isView"
          :isEdit="isEdit"
          @selMaterialId='selMaterialId'
        />
        <MaterialForBomView
          :materialCode="materialCode"
          v-show="currentMaterialId"
          :currentMaterialId="currentMaterialId"
          :formConfig="formConfig"
          ref="materialForBomViewRef"
        />
      </template>
    </FormCard>
  `,
});

export { BaseInfoForm };
