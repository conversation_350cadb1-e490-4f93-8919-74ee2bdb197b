import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath } from '../path';

export namespace BaseDataStaffApi {
  export interface BaseDataStaff {
    [key: string]: any;
    records: string;
    total: string;
  }
  /** 员工详细信息 */
  export interface StaffDetail {
    /** 头像附件id*/
    avatarId: string;
    /** 出生日期*/
    birthday: string;
    /** 	兼任岗位ID列表*/
    concurrentPositionIdList: string[];
    /** 	联系地址*/
    contactAddress: string;
    /** 联系电话*/
    contactNumber: string;
    /** 所属部门编号*/
    deptCode: string;
    /** 所属部门ID*/
    deptId: string;
    /** 	所属部门名称*/
    deptName: string;
    /** 主任岗位ID*/
    directorPositionId: string;
    /** 学历值*/
    educationalLevel: string;
    /** 学历标签 */
    educationalLevelLabel: string;
    /** 	邮箱*/
    email: string;
    /** 紧急联系人*/
    emergencyContact: string;
    /** 紧急联系电话*/
    emergencyContactNumber: string;
    /** 民族值*/
    ethnicGroup: string;
    /** 民族标签*/
    ethnicGroupLabel: string;
    /** 性别值*/
    genderType: string;
    /** 性别标签*/
    genderTypeLabel: string;
    /** 毕业院校*/
    graduationInstitution: string;
    /** 入职日期*/
    hireTime: string;
    /** 身份证号*/
    idCard: string;
    /** 可用状态*/
    isEnable: boolean;
    /** 籍贯*/
    placeOrigin: string;
    /** 籍贯编码*/
    placeOriginCode: string;
    /** 政治面貌值*/
    politicalStatus: string;
    /** 政治面貌标签*/
    politicalStatusLabel: string;
    /** 主任岗位编号*/
    positionCode: string;
    /** 主任岗位名称*/
    positionName: string;
    /** 	备注*/
    remark: string;
    /** 居住地址*/
    residentialAddress: string;
    /** 附件流水号*/
    serialNumber: string;
    /** 员工编号*/
    staffCode: string;
    /** 员工id */
    staffId: string;
    /** 员工姓名 */
    staffName: string;
    /** 离职日期*/
    terminationTime: string;
    /** 上级岗位 额外添加的*/
    upPositionName?: string;
    /** 关联用户id*/
    userId: string;
    /** 关联用户姓名 */
    username: string;
    /** 任职状态值*/
    workStatus: string;
    /** 任职状态标签 */
    workStatusLabel: string;
  }
}

/** 获取员工分页列表*/
export async function getStaffPageList(params: Recordable<any>) {
  return requestClient.post<Array<BaseDataStaffApi.BaseDataStaff>>(
    `${baseDataPath}/base/staff/getStaffPage`,
    { ...params },
  );
}

/** 新增员工*/
export async function createStaff(
  data: Omit<BaseDataStaffApi.BaseDataStaff, 'staffId'>,
) {
  return requestClient.post(`${baseDataPath}/base/staff/saveStaff`, data);
}
/** 修改员工信息*/
export async function updateStaff(
  data: Omit<BaseDataStaffApi.BaseDataStaff, 'staffId'>,
) {
  return requestClient.post(`${baseDataPath}/base/staff/modStaff`, data);
}

/** 根据员工id或编号获取员工基本信息*/
export async function getStaff(staffId?: string, staffCode?: string) {
  return requestClient.get(`${baseDataPath}/base/staff/getStaffInfo`, {
    params: {
      staffCode,
      staffId,
    },
  });
}

/** 根据员工id或编号获取员工详细信息*/
export async function getStaffDetail(staffId?: string, staffCode?: string) {
  return requestClient.get<BaseDataStaffApi.StaffDetail>(
    `${baseDataPath}/base/staff/getStaffDetail`,
    {
      params: {
        staffCode,
        staffId,
      },
    },
  );
}

/** 根据员工id禁用员工*/
export async function disableStaff(id: string) {
  return requestClient.get(`${baseDataPath}/base/staff/disableStaff/${id}`);
}

/** 根据员工id启用员工*/
export async function enableStaff(staffId: string) {
  return requestClient.get(`${baseDataPath}/base/staff/enableStaff/${staffId}`);
}

/** 员工列表模板下载 */
export async function staffTemplate() {
  return requestClient.get(`${baseDataPath}/base/staff/staffTemplate`, {
    responseReturn: 'raw',
    responseType: 'blob', // 设置响应类型为 blob
  });
}

/** 导出员工信息*/
export async function staffExport(params: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/staff/exportStaffs`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 员工导入*/
export async function staffImport(data: { file: Blob | File }) {
  return requestClient.upload(`${baseDataPath}/base/staff/importStaffs`, data);
}

/** 上传文件 单文件上传 */
export async function uploadFileList(data: any, config?: any) {
  return requestClient.post(`/file-manage/view/v1/file/upload`, data, config);
}

/** 获取行政区列表*/
export async function getAreaList(areaLevel?: string, parentCode?: string) {
  return requestClient.get(`${baseDataPath}/base/area/getAreaList`, {
    params: {
      areaLevel,
      parentCode,
    },
  });
}

/** 根据行政区划代码获取行政区详情*/
export async function getAreaDetail(areaCode: string) {
  return requestClient.get(
    `${baseDataPath}/base/area/getAreaByCode/${areaCode}`,
  );
}
