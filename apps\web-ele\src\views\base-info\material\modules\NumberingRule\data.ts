import type { VbenFormSchema } from '@girant/adapter';

import type { SelectOption } from '@vben/types';

import type { enumAPi } from '#/api/common';

import { computed, h, ref, watch } from 'vue';

import { getRuleList } from '#/api/base-info';
import { getEnumByName } from '#/api/common';

// 规则枚举
const ruleTypeEnumsList = ref<Array<enumAPi>>([]);

// 物料批次号生成类型
const batchNumGenerateList = ref<Array<SelectOption>>([]);
const batchNumGenerateListCom = computed(() => {
  return batchNumGenerateList.value;
});
// 物料序列号生成类型
const seqNumGenerateList = ref<Array<SelectOption>>([]);
const seqNumGenerateListCom = computed(() => {
  return seqNumGenerateList.value;
});

const getEnumByNameList = async () => {
  ruleTypeEnumsList.value = await getEnumByName('RuleTypeEnums');
};

watch(ruleTypeEnumsList, async () => {
  const [batchNumGenerateRes, seqNumGenerateRes] = await Promise.all([
    getRuleList({
      ruleType: ruleTypeEnumsList.value.find(
        (item) => item.enumKey === 'BATCH_NUM_GENERATE',
      )?.enumValue,
    }),
    getRuleList({
      ruleType: ruleTypeEnumsList.value.find(
        (item) => item.enumKey === 'SEQ_NUM_GENERATE',
      )?.enumValue,
    }),
  ]);

  batchNumGenerateList.value = batchNumGenerateRes.map((item: any) => ({
    label: item.ruleName,
    value: item.ruleCode,
  }));

  seqNumGenerateList.value = seqNumGenerateRes.map((item: any) => ({
    label: item.ruleName,
    value: item.ruleCode,
  }));
});

export function useNumberingRuleSchema(isViewMode: boolean): VbenFormSchema[] {
  getEnumByNameList();

  return [
    {
      component: isViewMode
        ? (props: any) => {
            return h('div', null, props.modelValue);
          }
        : 'Select',
      componentProps: {
        clearable: true,
        options: batchNumGenerateListCom,
      },

      fieldName: 'batchNumRule',
      label: '批次号生成规则',
      formItemClass: 'col-span-2',
    },

    {
      component: isViewMode
        ? (props: any) => {
            return h('div', null, props.modelValue ? '是' : '否');
          }
        : 'Switch',
      componentProps: {
        class: 'w-auto',
      },
      defaultValue: false,
      fieldName: 'isEnableSerialnum',
      formItemClass: 'col-start-1',
      label: '是否启用序列号',
      labelWidth: 110,
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Select',
      componentProps: {
        clearable: true,
        options: seqNumGenerateListCom,
      },
      dependencies: {
        componentProps(param) {
          if (!param.isEnableSerialnum) {
            param.serialnumRule = '';
          }
          return {
            disabled: !param.isEnableSerialnum,
          };
        },
        triggerFields: ['isEnableSerialnum'],
      },
      fieldName: 'serialnumRule',
      label: '序列号生成规则',
    },
  ];
}
