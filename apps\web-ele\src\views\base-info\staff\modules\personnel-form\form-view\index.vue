<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';

import { getPositionList } from '#/api/base-info';

import { useFormSchema } from './data';

/** 文件上传ref*/
const fileRef = ref();
/** 岗位列表 */
const postList = ref([]);
/** 文件列表回显控制 */
const uploadLoading = ref(false);
/** 人事资料表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});
onMounted(async () => {
  postList.value = await getPositionList({});
});

defineExpose({
  fileRef,
  formApi,
  uploadLoading,
});
</script>

<template>
  <Form>
    <template #concurrentPositionIdList="row">
      <div>
        {{
          postList
            .filter((post: any) => row.value?.includes(post.positionId))
            .map((post: any) => post.positionName)
            .join('、') || '/'
        }}
      </div>
    </template>
  </Form>
</template>
