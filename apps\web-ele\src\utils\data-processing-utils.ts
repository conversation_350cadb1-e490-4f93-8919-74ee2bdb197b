/**
 * 检查值是否为空
 * @param value 要检查的值
 * @returns 如果值为空返回 true，否则返回 false
 */
const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) {
    return true;
  }
  if (typeof value === 'string') {
    return value.trim() === '';
  }
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }
  return false;
};

/**
 * 检查对象中的每个元素是否为空
 * @param obj 要检查的对象
 * @returns 包含检查结果的对象
 */
export const checkObjectFields = (
  obj: Record<string, any>,
): {
  emptyFields: string[];
  message: string;
  valid: boolean;
} => {
  const emptyFields: string[] = [];

  for (const [key, value] of Object.entries(obj)) {
    if (isEmpty(value)) {
      emptyFields.push(key);
    }
  }

  if (emptyFields.length > 0) {
    return {
      valid: false,
      message: `对象包含 ${emptyFields.length} 个空字段`,
      emptyFields,
    };
  }

  return {
    valid: true,
    message: '对象中所有字段均不为空',
    emptyFields: [],
  };
};

/**
 * 检查对象数组中的每个元素是否为空
 * @param arr 要检查的对象数组
 * @returns 包含检查结果的对象
 */
export const checkObjectArray = (
  arr: Record<string, any>[],
): {
  invalidIndices: number[];
  message: string;
  valid: boolean;
} => {
  const invalidIndices: number[] = [];

  arr.forEach((obj, index) => {
    if (typeof obj !== 'object' || obj === null) {
      invalidIndices.push(index);
      return;
    }

    const isObjEmpty = Object.keys(obj).length === 0;
    if (isObjEmpty) {
      invalidIndices.push(index);
      return;
    }

    const hasEmptyFields = Object.values(obj).some((value) => isEmpty(value));
    if (hasEmptyFields) {
      invalidIndices.push(index);
    }
  });

  if (invalidIndices.length > 0) {
    return {
      valid: false,
      message: `数组包含 ${invalidIndices.length} 个无效元素`,
      invalidIndices,
    };
  }

  return {
    valid: true,
    message: '数组中所有元素均有效',
    invalidIndices: [],
  };
};

/**
 * 根据指定字段和值在对象数组中查找匹配的对象
 * @param arr 要查找的对象数组
 * @param field 要匹配的字段名
 * @param value 要匹配的值
 * @returns 包含匹配结果的对象
 */

export const findObjectInArray = (
  arr: Record<string, any>[],
  field: string,
  value: any,
): {
  found: boolean;
  matchedObject?: Record<string, any>;
  message: string;
} => {
  const matchedObject = arr.find((obj) => obj[field] === value);

  if (matchedObject) {
    return {
      found: true,
      message: '找到匹配的对象',
      matchedObject,
    };
  }

  return {
    found: false,
    message: '未找到匹配的对象',
  };
};

// import { isEmpty } from '@vben/utils';
/**
 * 检查对象数据是否全部为空
 * @param obj 要检查的对象
 * @returns 如果全部值为空返回 true，否则返回 false
 */
export function isObjectEmpty(obj: Record<string, any>): boolean {
  if (!obj || Object.keys(obj).length === 0) {
    return true;
  }
  return Object.values(obj).every((value) => isEmpty(value));
}
