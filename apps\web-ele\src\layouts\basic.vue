<script lang="ts" setup>
import type { NotificationItem } from '@vben/layouts';

import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationLoginExpiredModal, useVbenModal } from '@vben/common-ui';
import { useWatermark } from '@vben/hooks';
import {
  BasicLayout,
  LockScreen,
  Notification,
  UserDropdown,
} from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';

import { ElNotification } from 'element-plus';

import { filePreview, getReceiveMsgPage } from '#/api';
import { useAuthStore } from '#/store';
import { WS } from '#/utils/socket/common-socketio';
import LoginForm from '#/views/_core/authentication/login.vue';
// import UserForm from '#/views/system-settings/user/modules/form-view/index.vue';
// import UserForm from '#/views/_core/authentication/personal-info.vue';
import ResetPassword from '#/views/_core/authentication/reset-password.vue';
// import StaffForm from '#/views/_core/authentication/staff-info.vue';
// import MessageFormView from '#/views/system-settings/message/receive-message/modules/FormView.vue';

import OrganizationalDropdown from './component/organizational-dropdown.vue';

const router = useRouter();
const viewMessageId = ref();
const notifications = ref<NotificationItem[]>([]);
const avatarUrl = ref(preferences.app.defaultAvatar);

const userStore = useUserStore();
const userInfo = ref(userStore.userInfo);
const authStore = useAuthStore();
const accessStore = useAccessStore();
const { destroyWatermark, updateWatermark } = useWatermark();
const showDot = computed(() =>
  notifications.value.some((item) => !item.isRead),
);

const menus = computed(() => {
  const baseMenus = [
    {
      handler: () => {
        PasswordFormModalApi.setState({ title: '修改密码' }).open();
      },
      text: '修改密码',
    },
  ];
  if (userInfo.value?.currentUserType === 1) {
    return [
      {
        handler: () => {
          FormModalApi.setState({ title: '个人信息' }).open();
        },
        text: '个人信息',
      },
      ...baseMenus,
    ];
  }
  return baseMenus;
});

const currentAccountInfo = computed(() => ({
  text: `${userInfo.value?.nickname}（${userInfo.value?.username}）`,
  description:
    [
      userInfo.value?.currentOrgName,
      userInfo.value?.deptName,
      userInfo.value?.directorPositionName,
    ]
      .filter(Boolean)
      .join(' | ') ||
    userInfo.value?.supplierName ||
    userInfo.value?.customerName,
  tagList: [
    { text: userInfo.value?.currentUserTypeLabel },
    ...(userInfo.value?.currentUserType === 1
      ? [{ text: `入职${userInfo.value?.hireMonth}个月` }]
      : []),
    ...(userInfo.value?.currentUserType === 21
      ? [
          { text: userInfo.value?.companyTypeLabel },
          { text: userInfo.value?.companyScaleLabel },
        ]
      : []),
  ],
}));

const avatar = computed(() => avatarUrl.value);

const isShowOrgList = computed(() => {
  return userInfo.value?.orgList.length > 0;
});

const modalUserFormRef = ref();
const [FormModal, FormModalApi] = useVbenModal({
  footer: true,
  closeOnClickModal: false,
  confirmText: '保存',
  cancelText: '关闭',
  showConfirmButton: true,
  onConfirm: () => {
    modalUserFormRef.value.FormApi.validateAndSubmitForm();
  },
});

const [MessageFormModal, MessageFormModalApi] = useVbenModal({
  footer: true,
  closeOnClickModal: false,
  showConfirmButton: false,
  showCancelButton: true,
  cancelText: '关闭',
});

const modalFormRef = ref();
const [PasswordFormModal, PasswordFormModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    modalFormRef.value.formApi.validateAndSubmitForm();
  },
  showCancelButton: true,
});

const handleMessage = (message: any) => {
  // console.error('业务页面接收到订阅消息:', message);
  const msgData = JSON.parse(message.content);

  ElNotification({
    title: msgData.messageTitle,
    message: msgData.messageContent,
    type: 'success',
    onClick: () => {
      readMessage(msgData.messageId);
    },
  });
  getReceiveMsg();
};

const readMessage = (messageId: string) => {
  viewMessageId.value = messageId;
  MessageFormModalApi.setState({ title: '消息' }).open();
  getReceiveMsg();
};

const getReceiveMsg = async () => {
  const response = await getReceiveMsgPage({
    pageSize: 5,
    pageNum: 1,
    isRead: false,
  });
  notifications.value = response.records;
};

// const refreshUserInfo = async () => {
//   userInfo.value = userStore.userInfo;
//   await renderAvatar();

//   FormModalApi.close();
// };

const renderAvatar = async () => {
  if (userInfo.value?.avatar) {
    try {
      const blob = await filePreview(userInfo.value.avatar);
      avatarUrl.value = URL.createObjectURL(blob);
    } catch {} // 忽略错误，保持默认头像
  }
};

onMounted(async () => {
  getReceiveMsg();
  await WS.subscribeTopic('msg:system');
  // 监听站内信消息事件
  WS.on('msg:system', handleMessage);

  await renderAvatar();
});

async function handleLogout() {
  await authStore.logout(false);
}

// function handleNoticeClear() {
//   // notifications.value = [];
// }

function handleViewAll() {
  router.push('/system-settings/receive-message');
}

function handleRead(item: any) {
  readMessage(item.messageId);
}

function handleClickLogo() {}

watch(
  () => preferences.app.watermark,
  async (enable) => {
    if (enable) {
      await updateWatermark({
        content: `${userInfo.value?.username}`,
      });
    } else {
      destroyWatermark();
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <BasicLayout
    @clear-preferences-and-logout="handleLogout"
    @click-logo="handleClickLogo"
  >
    <template #header-right-2>
      <OrganizationalDropdown
        v-if="isShowOrgList"
        :org-list="userInfo?.orgList"
        :current-org-name="userInfo?.currentOrgName"
      />
    </template>
    <template #user-dropdown>
      <UserDropdown
        :avatar
        :menus
        :text="currentAccountInfo.text"
        :description="currentAccountInfo.description"
        :tag-list="currentAccountInfo.tagList"
        @logout="handleLogout"
      />
    </template>
    <template #notification>
      <Notification
        :dot="showDot"
        :notifications="notifications"
        :show-clear="false"
        @view-all="handleViewAll"
        @read="handleRead"
      />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal
        v-model:open="accessStore.loginExpired"
        :avatar
      >
        <LoginForm />
      </AuthenticationLoginExpiredModal>
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
  </BasicLayout>

  <FormModal class="w-6/12">
    <!-- <StaffForm
      ref="modalUserFormRef"
      @personal-update-success="refreshUserInfo"
    /> -->
  </FormModal>
  <PasswordFormModal>
    <ResetPassword ref="modalFormRef" />
  </PasswordFormModal>
  <MessageFormModal class="w-8/12">
    <!-- <MessageFormView :message-id="viewMessageId" /> -->
  </MessageFormModal>
</template>
