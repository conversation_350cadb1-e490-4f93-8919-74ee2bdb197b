import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';

export function useMaterialForBomFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'materialCode',
      label: '物料编号',
    },
    // {
    //   component: (props: any) => {
    //     return h('div', null, props.modelValue || '/');
    //   },
    //   fieldName: 'materialName',
    //   label: '物料名称',
    // },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialAlias',
      label: '物料别名',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialSpecs',
      label: '物料规格',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'baseUnitLabel',
      label: '基本单位',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialAttributeLabel',
      label: '物料属性',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialTypeLabel',
      label: '物料大类',
    },
    {
      component: (props: any) => {
        return props.modelValue
          ? h(ImageViewer, {
              imgId: props.modelValue,
              imgCss: 'size-40',
              imgFit: 'cover',
              class: '!w-[160px]',
            })
          : h('div', null, '/');
      },
      fieldName: 'pictureFileId',
      formItemClass: 'col-start-1',
      label: '图片',
    },
  ];
}
