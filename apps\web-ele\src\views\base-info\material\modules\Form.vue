<script lang="ts" setup>
import { defineProps, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElMessage, ElMessageBox } from 'element-plus';

import {
  getMaterialDetail,
  modMaterial,
  saveMaterial,
  saveMaterialFalse,
} from '#/api/base-info';

import { BaseInfoForm } from './BaseInfoForm/index';
import { Inventory } from './Inventory/index';
import { NumberingRule } from './NumberingRule/index';
import { Purchase } from './Purchase/index';

const props = defineProps({
  isEdit: {
    default: false,
    type: Boolean,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  materialCode: {
    default: '',
    type: String,
  },
  materialId: {
    default: '',
    type: String,
  },
});
const emits = defineEmits(['isSubmit', 'submitState']);

const loading = ref(false);
/** 合并公共表单配置*/
const formConfig = {
  commonConfig: {
    componentProps: { class: 'w-full' },
    hideRequiredMark: props.isView,
  },

  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
};

type ComponentName =
  | 'BaseInfoForm'
  | 'Inventory'
  | 'NumberingRule'
  | 'Purchase';

// 组件引用映射
interface ComponentRefMap {
  [key: string]: {
    methods: {
      getValue: string; // 获取值的方法名
      setValue: string; // 设置值的方法名
    };
    ref: any;
  };
}

/** 物料信息 */
const BaseInfoFormRef = ref();

/** 编号规则 */
const NumberingRuleRef = ref();

/** 库存配置 */
const InventoryRef = ref();

/** 采购配置 */
const PurchaseRef = ref();

// 组件引用和方法名映射
const componentRefs: ComponentRefMap = {
  BaseInfoForm: {
    methods: {
      getValue: 'getBaseInfoFormValues',
      setValue: 'loadData',
    },
    ref: BaseInfoFormRef,
  },

  NumberingRule: {
    methods: {
      getValue: 'getNumberingRuleValues',
      setValue: 'loadData',
    },
    ref: NumberingRuleRef,
  },
  Inventory: {
    methods: {
      getValue: 'getInventoryValues',
      setValue: 'loadData',
    },
    ref: InventoryRef,
  },
  Purchase: {
    methods: {
      getValue: 'getPurchaseValues',
      setValue: 'loadData',
    },
    ref: PurchaseRef,
  },
};

/** 确认对话框抽象*/
function confirm(content: string) {
  return ElMessageBox.confirm(content, '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  });
}

/**
 * 调用组件方法
 * @param componentName 组件名称
 * @param methodType 方法类型 ('getValue' | 'setValue')
 * @param data 传递给方法的数据（用于setValue）
 * @returns 方法执行结果
 */
const callComponentMethod = async (
  componentName: ComponentName,
  methodType: 'getValue' | 'setValue',
  data?: any,
) => {
  const component = componentRefs[componentName];
  if (!component) {
    throw new Error(`组件 ${componentName} 不存在`);
  }

  const methodName = component.methods[methodType];
  const refValue = component.ref.value;

  if (!refValue || typeof refValue[methodName] !== 'function') {
    throw new Error(`组件 ${componentName} 没有 ${methodName} 方法`);
  }

  if (methodType === 'setValue' && data !== undefined) {
    return await refValue[methodName](data);
  }

  return await refValue[methodName]();
};

/**
 * 获取所有组件的值
 * @returns 包含所有组件值的数组
 */
const getAllComponentValues = async () => {
  const promises = Object.keys(componentRefs).map((name) =>
    callComponentMethod(name as ComponentName, 'getValue'),
  );

  return await Promise.all(promises);
};

/**
 * 设置所有组件的值
 * @param data 要设置的数据
 */
const setAllComponentValues = (data: any) => {
  Object.keys(componentRefs).forEach((name) => {
    callComponentMethod(name as ComponentName, 'setValue', data);
  });
};

onMounted(async () => {
  if (!isEmpty(props.materialId) || !isEmpty(props.materialCode)) {
    try {
      loading.value = true;
      const allData = await getMaterialDetail({
        materialCode: props.materialCode,
        materialId: props.materialId,
      });
      allData.quantityDecimal = JSON.stringify(allData.quantityDecimal);
      setAllComponentValues(allData);
      loading.value = false;
    } catch {
      loading.value = false;
    }
  }
});

const submitAllForm = async () => {
  if (!(await confirm('确定提交吗？'))) {
    return;
  }

  try {
    emits('isSubmit', true);
    // 获取所有组件的值
    const results = await getAllComponentValues();

    const mergedData = Object.assign({}, ...results);

    if (props.materialId) {
      mergedData.materialId = props.materialId;
      modMaterial(mergedData);
      ElMessage.success('编辑成功');
    } else {
      await (mergedData.isStandard
        ? saveMaterial(mergedData)
        : saveMaterialFalse(mergedData));
      ElMessage.success('提交成功');
    }

    emits('isSubmit', false);
    emits('submitState', true);
  } catch (error) {
    emits('isSubmit', false);
    emits('submitState', false);

    // 从错误信息中识别失败的任务
    const errorMessage = error instanceof Error ? error.message : '提交失败';
    ElMessage.error(errorMessage);
  }
};

defineExpose({ submitAllForm });
</script>

<template>
  <div v-loading="loading">
    <BaseInfoForm
      ref="BaseInfoFormRef"
      :is-view="isView"
      :is-edit="isEdit"
      :form-config="formConfig"
      :material-id="materialId"
      :material-code="materialCode"
      @base-info-submit-state="(val: boolean) => emits('submitState', val)"
    />

    <NumberingRule
      ref="NumberingRuleRef"
      :is-view="isView"
      :is-edit="isEdit"
      :form-config="formConfig"
      :material-id="materialId"
      :material-code="materialCode"
    />

    <Inventory
      ref="InventoryRef"
      :is-view="isView"
      :is-edit="isEdit"
      :form-config="formConfig"
      :material-id="materialId"
      :material-code="materialCode"
    />

    <Purchase
      ref="PurchaseRef"
      :is-view="isView"
      :is-edit="isEdit"
      :form-config="formConfig"
      :material-id="materialId"
      :material-code="materialCode"
    />
  </div>
</template>
