import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { BaseDataStaffApi } from '#/api/base-info';

import { h, markRaw } from 'vue';

import { useAccess } from '@vben/access';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElCascader, ElInputTag } from 'element-plus';

import { getDictItemList } from '#/api';
import { getAllDeptTree, getPositionList } from '#/api/base-info';

const { hasAccessByCodes } = useAccess();
/** 查询 */
export async function useGridFormSchema(): Promise<VbenFormSchema[]> {
  /** 查询部门树 */
  const getDeptTree = async () => {
    const res = await getAllDeptTree({ isQueryPosition: true });
    // 处理结果
    return res;
  };
  const deptTree = await getDeptTree();
  console.log('deptTree', deptTree);
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'staffCodeList',
      label: '员工编号',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'staffName',
      label: '员工姓名',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const workStatusTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          workStatusTypeList.push({ label: '全部', value: '' });
          return workStatusTypeList;
        },
        api: () => {
          return getDictItemList('baseWorkStatus');
        },
        clearable: true,
      },
      defaultValue: '00',
      fieldName: 'workStatusList',
      label: '任职状态',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '全部', value: '' },
          { label: '正常', value: true },
          { label: '停用', value: false },
        ],
      },
      fieldName: 'isEnable',
      label: '身份状态',
    },
    {
      component: 'Input',
      fieldName: 'hireTime',
      formItemClass: 'col-span-2',
      label: '入职日期',
    },
    {
      component: 'Input',
      fieldName: 'terminationTime',
      formItemClass: 'col-span-2',
      label: '离职日期',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'contactNumber',
      formItemClass: 'col-span-1',
      label: '联系电话',
    },
    {
      component: h(ElCascader, {
        clearable: true,
        filterable: true,
        'max-collapse-tags': 1,
        'show-all-levels': false,
        options: deptTree,
        props: {
          multiple: true,
          value: 'deptId',
          label: 'deptName',
          children: 'children',
        },
      }),
      componentProps: {},
      fieldName: 'deptIdList',
      formItemClass: 'col-span-1',
      label: '所属部门',
    },
    // {
    //   component: 'ApiTreeSelect',
    //   componentProps: {
    //     afterFetch: (data: any) => {
    //       // 转换函数
    //       function convertDeptData(dept: any) {
    //         return {
    //           label: `【${dept.deptCode}】${dept.deptName}`,
    //           value: dept.deptId,
    //           children: dept.children
    //             ? dept.children.map((child: any) => convertDeptData(child))
    //             : [],
    //         };
    //       }
    //       // 执行转换
    //       const convertedData = data.map((dept: any) => convertDeptData(dept));
    //       return convertedData;
    //     },
    //     api: () => {
    //       return getAllDeptTree({});
    //     },
    //     checkStrictly: true,
    //     clearable: true,
    //     collapseTags: true,
    //     collapseTagsTooltip: true,
    //     filterable: true,
    //     maxCollapseTags: 1,
    //     multiple: true,
    //   },
    //   fieldName: 'deptIdList',
    //   formItemClass: 'col-span-1',
    //   label: '所属部门',
    // },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        filterable: true,
        maxCollapseTags: 1,
        multiple: true,
        options: [],
        placeholder: '请选择',
      },
      defaultValue: [],
      dependencies: {
        async componentProps(values) {
          const positionList: any[] = [];
          if (values.deptIdList && values.deptIdList.length > 0) {
            // 获取部门下的岗位
            const requests = values.deptIdList.map((item: string) =>
              getPositionList({ deptIdList: item }),
            );
            const responses = await Promise.all(requests);
            // 合并结果
            responses.forEach((res) => {
              if (res.length > 0) {
                positionList.push(
                  ...res.map(
                    (item: { positionId: string; positionName: string }) => ({
                      label: item.positionName,
                      value: item.positionId,
                    }),
                  ),
                );
              }
            });
            // 过滤掉positionIdList不存在positionList中的岗位
            values.positionIdList = values.positionIdList.filter(
              (position: string) =>
                positionList.some((item: any) => item.value === position),
            );
          } else {
            let res = await getPositionList({});
            res = res.map((item: any) => ({
              label: item.positionName,
              value: item.positionId,
            }));
            positionList.push(...res);
          }
          return {
            options: positionList,
          };
        },
        triggerFields: ['deptIdList'],
      },
      fieldName: 'positionIdList',
      formItemClass: 'col-span-1',
      label: '任职岗位',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '绑定', value: true },
          { label: '未绑定', value: false },
        ],
      },
      dependencies: {
        if() {
          return hasAccessByCodes(['sys:user:normal:query:list']);
        },
        triggerFields: ['isBindUser'],
      },
      defaultValue: undefined,
      fieldName: 'isBindUser',
      label: '关联用户账号',
      labelWidth: 95,
    },
  ];
}

/** 表格 */
export function useColumns<T = BaseDataStaffApi.BaseDataStaff>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  const operationColumn = {
    align: 'center',
    cellRender: {
      attrs: {
        nameField: 'staff',
        nameTitle: '员工设置',
        onClick: onActionClick,
      },
      name: 'CellOperation',
      options: [
        ...(hasAccessByCodes(['base:employee:query:detail']) ? ['view'] : []),
        ...(hasAccessByCodes(['base:employee:edit:modify']) ? ['edit'] : []),
      ],
    },
    field: 'operation',
    fixed: 'right',
    minWidth: 150,
    title: '操作',
  } as NonNullable<VxeTableGridOptions['columns']>[0];
  return [
    { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
    {
      field: 'staffCode',
      title: '员工编号',
      width: 200,
    },
    {
      field: 'avatarId',
      minWidth: 100,
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.avatarId,
            imgCss: 'h-[50px]',
          }),
      },
      title: '头像',
    },
    {
      field: 'staffName',
      minWidth: 100,
      title: '员工姓名',
    },

    {
      field: 'genderTypeLabel',
      minWidth: 100,
      title: '性别',
    },
    {
      field: 'contactNumber',
      minWidth: 150,
      title: '联系电话',
    },
    {
      field: 'deptName',
      title: '部门名称',
      width: 200,
    },
    {
      field: 'directorPositionName',
      title: '主任岗位',
      width: 200,
    },
    {
      field: 'workStatusLabel',
      minWidth: 100,
      slots: {
        default: 'workStatus',
      },
      title: '任职状态',
    },
    {
      field: 'hireTime',
      slots: {
        default: 'hireTime',
      },
      title: '在职时间',
      width: 200,
    },
    {
      cellRender: {
        attrs: {
          activeText: '已启用',
          beforeChange: onStatusChange,
          inactiveText: '已禁用',
        },
        name: hasAccessByCodes([
          'base:employee:enable:enable',
          'base:employee:enable:disable',
        ])
          ? 'CellSwitch'
          : 'CellTag',
      },
      field: 'isEnable',
      minWidth: 100,
      title: '启用状态',
    },
    ...(hasAccessByCodes([
      'base:employee:edit:modify',
      'base:employee:query:detail',
    ])
      ? [operationColumn]
      : []),
  ];
}
