import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath, systemPath } from '../path';

export namespace BaseDataPositionApi {
  export interface BaseDataPosition {
    [key: string]: any;
    positionId: string;
  }
  export type getPositionListType = {
    deptIdList?: string;
    isEnable?: boolean;
    parentId?: string;
    positionCodeList?: string;
    positionName?: string;
    positionTypeList?: string;
  };
  /** 停用岗位返回信息 */
  export interface DisablePositionRes {
    success: boolean;
    msg: string;
    code: number;
    data: {
      /** 下级岗位名称列表 */
      positionNameList: string[];
      /** 员工数 */
      staffCount: number;
    };
  }
}

/** 新增岗位信息 */
export async function savePosition(
  data: Omit<BaseDataPositionApi.BaseDataPosition, 'positionId'>,
) {
  return requestClient.post(`${baseDataPath}/base/position/savePosition`, data);
}

/** 修改岗位信息 */
export async function modPosition(
  data: Omit<BaseDataPositionApi.BaseDataPosition, 'positionId'>,
) {
  return requestClient.post(`${baseDataPath}/base/position/modPosition`, data);
}

/** 导入岗位信息 */
export async function importPosition(data: { file: Blob | File }) {
  return requestClient.upload(
    `${baseDataPath}/base/position/importPosition`,
    data,
  );
}

/**  查询岗位列表*/
export async function getPositionList(
  data: BaseDataPositionApi.getPositionListType,
) {
  return requestClient.post(
    `${baseDataPath}/base/position/getPositionList`,
    data,
  );
}

/** 查询岗位分页列表 */
export async function getDeptAndPositionPage(params: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/position/getDeptAndPositionPage`,
    params,
  );
}

/** 导出岗位信息 */
export async function exportPosition(params: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/position/exportPosition`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 岗位模板下载 */
export async function positionTemplate() {
  return requestClient.get(`${baseDataPath}/base/position/positionTemplate`, {
    responseReturn: 'raw',
    responseType: 'blob', // 设置响应类型为 blob
  });
}

/** 根据岗位id或编号获取岗位信息 */
export async function getPositionDetail(
  positionId?: string,
  positionCode?: string,
) {
  return requestClient.get(`${baseDataPath}/base/position/getPositionInfo`, {
    params: {
      positionCode,
      positionId,
    },
  });
}

/** 启用岗位 */
export async function enablePosition(positionId: string) {
  return requestClient.get(
    `${baseDataPath}/base/position/enablePosition/${positionId}`,
  );
}

/** 停用岗位 */
export async function disablePosition(
  positionId: string,
  isForceDisable?: boolean,
) {
  return requestClient.get<BaseDataPositionApi.DisablePositionRes>(
    `${baseDataPath}/base/position/disablePosition`,
    {
      params: {
        positionId,
        isForceDisable,
      },
      responseReturn: 'body',
    },
  );
}

/** 获取全部角色 */
export async function getAllRoleList() {
  return requestClient.get(`${systemPath}/role/getAllRoleList`);
}
