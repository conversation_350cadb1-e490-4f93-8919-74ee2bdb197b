import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { UploadFiles } from '@girant-web/upload-files-component';
import { UploadPic } from '@girant-web/upload-pic-component';
import { z } from '@girant/adapter';

import { getIndustryList } from '#/api/base-info';
import { getDictItemList } from '#/api/common';

export function useProfileInfoFormSchema(
  isViewMode: boolean,
): VbenFormSchema[] {
  return [
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'legalPerson',
      label: '法人',
      rules: z.string().min(1, '请输入法人').max(20, '长度不超过20'),
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'DatePicker',
      componentProps: {
        class: 'flex-auto',
        disabledDate: (time: Date) => {
          return time.getTime() > Date.now();
        },
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'establishmentDate',
      label: '创立日期',
      rules: 'required',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, `${showText} 万元`);
          }
        : 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 0,
      },
      fieldName: 'registeredCapital',
      label: '注册资金',
      rules: 'required',
      suffix: () =>
        isViewMode ? '' : h('span', { class: 'text-nowrap' }, '万元'),
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, `${showText} ㎡`);
          }
        : 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 0,
      },
      fieldName: 'landArea',
      formItemClass: 'col-span-2',
      label: '公司(厂区)面积',
      suffix: () => (isViewMode ? '' : h('span', null, '㎡')),
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.registeredAddress) {
            return z.string().max(1000, { message: '注册地址长度不超过1000' });
          }
          return null;
        },
        triggerFields: ['registeredAddress'],
      },
      fieldName: 'registeredAddress',
      formItemClass: 'col-span-full',
      label: '注册地址',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const sexTypeList = data.map((item: any) => ({
            label: item.industryName,
            value: item.industryCategory,
          }));

          return sexTypeList;
        },
        api: () => {
          return getIndustryList();
        },
        clearable: true,
      },
      defaultValue: '',
      fieldName: isViewMode ? 'industryCategoryLabel' : 'industryCategory',
      label: '行业类别',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: [], // 初始为空
      },
      defaultValue: '',
      dependencies: {
        async componentProps() {
          const data = await getDictItemList('baseTaxNature');
          return {
            options: data.map((item: any) => ({
              label: item.dictLabel,
              value: item.dictValue,
            })),
          };
        },
        triggerFields: [''],
      },
      fieldName: isViewMode ? 'taxNatureLabel' : 'taxNature',
      label: '企业规模',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.businessRegistrationNumber) {
            return z.string().max(100, { message: '工商登记号长度不超过100' });
          }
          return null;
        },
        triggerFields: ['businessRegistrationNumber'],
      },
      fieldName: 'businessRegistrationNumber',
      label: '工商登记号',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.productionLicense) {
            return z
              .string()
              .max(100, { message: '生产经营许可证长度不超过100' });
          }
          return null;
        },
        triggerFields: ['productionLicense'],
      },
      fieldName: 'productionLicense',
      label: '生产经营许可证',
      labelWidth: 120,
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.socialCreditId) {
            return z
              .string()
              .max(100, { message: '统一社会信用代码长度不超过100' });
          }
          return null;
        },
        triggerFields: ['socialCreditId'],
      },
      fieldName: 'socialCreditId',
      label: '统一社会信用代码',
      labelWidth: 125,
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.website) {
            return z.string().max(1024, { message: '公司网址长度不超过1024' });
          }
          return null;
        },
        triggerFields: ['website'],
      },
      fieldName: 'website',
      label: '公司网址',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 100,
        placeholder: '请输入',
        showWordLimit: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.mainBusiness) {
            return z.string().max(100, { message: '主营业务长度不超过100' });
          }
          return null;
        },
        triggerFields: ['mainBusiness'],
      },
      fieldName: 'mainBusiness',
      formItemClass: 'col-span-full items-start',
      label: '主营业务',
    },
    {
      component: (props: any) => {
        return isViewMode
          ? h(ImageViewer, {
              imgId: props.modelValue,
              imgCss: 'size-40',
              imgFit: 'cover',
              class: '!w-[160px]',
            })
          : h(UploadPic, {
              imgId: props.modelValue,
              allowedFormats: ['jpg'],
              textConfig: {
                promptText: '上传LOGO',
                tipText: '只支持 .jpg 格式',
              },
            });
      },
      modelPropName: 'imgId',
      fieldName: 'logoFileId',
      formItemClass: 'col-span-full  col-start-1 items-start',
      label: '公司LOGO',
    },
    {
      component: h(UploadFiles, {
        mode: isViewMode ? 'readMode' : 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'trademarkSerialNumber',
      formItemClass: 'col-span-full items-start',
      label: '注册商标',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 0,
      },
      fieldName: 'totalEmployees',
      formItemClass: 'col-start-1',
      label: '企业总人数',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 0,
      },
      fieldName: 'managementStaffCount',
      label: '管理人员人数',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 0,
      },
      fieldName: 'rdStaffCount',
      label: '研发人员人数',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 0,
      },
      fieldName: 'qualityControlStaffCount',
      label: '质量人员人数',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 0,
      },
      fieldName: 'productionStaffCount',
      label: '生产人员人数',
    },
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 0,
      },
      fieldName: 'technicalStaffCount',
      label: '技术人员人数',
    },
  ];
}
