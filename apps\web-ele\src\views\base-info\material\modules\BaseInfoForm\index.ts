import { computed, defineComponent, ref } from 'vue';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import Edit from './edit.vue';
import View from './view.vue';

const BaseInfoForm = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    View,
  },
  emits: ['baseInfoSubmitState'],
  name: 'BaseInfoForm',
  props: {
    formConfig: {
      default: () => ({}),
      type: Object,
    },
    isView: {
      default: false,
      type: Boolean,
    },
    materialCode: {
      default: '',
      type: String,
    },
    materialId: {
      default: '',
      type: String,
    },
  },
  setup(props, { expose }) {
    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();

    const getBaseInfoFormValues = async () => {
      if (componentRef.value?.onSubmitToBaseInfoForm) {
        return await componentRef.value.onSubmitToBaseInfoForm();
      }
      return null;
    };

    const loadData = (data: any) => {
      componentRef.value.loadData(data);
    };

    expose({
      getBaseInfoFormValues,
      loadData,
    });

    return {
      componentRef,
      currentComponent,
    };
  },
  template: `
    <FormCard title="物料信息">
      <component
        ref="componentRef"
        :is="currentComponent"
        :materialCode="materialCode"
        :materialId="materialId"
        :formConfig="formConfig"
        :isView="isView"
      />
    </FormCard>
  `,
});

export { BaseInfoForm };
