import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { z } from '@girant/adapter';

import { getMaterialBomPage } from '#/api/base-info';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';

const fetchMaterial =
  () =>
  async ({
    keyword,
    pageNum,
    pageSize,
  }: {
    keyword: string;
    pageNum: number;
    pageSize: number;
  }) => {
    return await getMaterialBomPage({
      materialName: keyword,
      // 制成品、半成品
      materialTypeList: ['00', '10'],
      isEnable: true,
      pageNum,
      pageSize,
    });
  };

export function useBaseInfoFormSchema(
  isViewMode: boolean,
  isEditMode?: boolean,
  onChangeSel?: (materialId: string) => void,
): VbenFormSchema[] {
  return [
    {
      component:
        isViewMode || isEditMode
          ? (props: any) => {
              return h('div', null, props.modelValue);
            }
          : h(RemoteSearchSelect, {
              class: 'h-1/4',
              fetchMethod: fetchMaterial(),
              labelKey: 'materialName',
              subLabelKey: 'materialCode',
              onChange: onChangeSel,
              valueKey: 'materialId',
            }),
      fieldName: isViewMode || isEditMode ? 'materialName' : 'materialId',
      label: '母件名称',
      modelPropName: 'modelValue',
      rules: 'required',
    },

    {
      component: 'Input',
      fieldName: isViewMode ? 'currentVersion' : 'version',
      label: '版本号',
      rules: z
        .string()
        .min(1, '版本号不能为空')
        .max(256, '版本号长度不能超过256'),
      labelWidth: 60,
    },

    {
      component: (props: any) => {
        return h('div', null, props.modelValue);
      },
      fieldName: 'createUserName',
      label: '创建人',
      dependencies: {
        if: isViewMode,
        triggerFields: ['createUserName'],
      },
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue);
      },
      fieldName: 'createTime',
      label: '创建时间',
      dependencies: {
        if: isViewMode,
        triggerFields: ['createTime'],
      },
    },
  ];
}
