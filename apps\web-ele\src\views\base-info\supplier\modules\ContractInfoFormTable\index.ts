import { computed, defineComponent, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import Edit from './edit.vue';
import View from './view.vue';

const ContractInfoFormTable = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    View,
  },
  name: 'ContractInfoFormTable',
  props: {
    customerCode: {
      default: '',
      type: String,
    },

    isView: {
      default: false,
      type: Boolean,
    },
    supplierId: {
      default: '',
      type: String,
    },
  },
  setup(props, { expose }) {
    const isEdit = computed(() => !isEmpty(props.supplierId));
    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();

    const getContractInfoFormTableValues = async () => {
      if (componentRef.value?.onSubmitToFormTable) {
        return await componentRef.value.onSubmitToFormTable();
      }
      return null;
    };

    const handleEdit = async () => {
      componentRef.value.onSubmitToFormTable();
    };

    expose({
      getContractInfoFormTableValues,
    });

    return {
      componentRef,
      currentComponent,
      handleEdit,
      isEdit,
    };
  },
  template: `
    <FormCard title="采购合同信息">
      <component
        ref="componentRef"
        :is="currentComponent"
        :customerCode="customerCode"
        :supplierId="supplierId"
        :isView="isView"
        :isEdit="isEdit"
       
      />
    </FormCard>
  `,
});

export { ContractInfoFormTable };
