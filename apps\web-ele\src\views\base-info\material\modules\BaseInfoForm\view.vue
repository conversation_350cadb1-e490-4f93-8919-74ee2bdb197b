<script lang="ts" setup>
import { defineProps, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';

import { useBaseInfoFormSchema } from './data';

const props = defineProps({
  formConfig: {
    default: () => {},
    type: Object,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  materialCode: {
    default: '',
    type: String,
  },
  materialId: {
    default: '',
    type: String,
  },
});

const loading = ref(false);

/** 基础资料表单 */
const [Form, BaseInfoFormApi] = useVbenForm({
  ...props.formConfig,
  schema: useBaseInfoFormSchema(props.isView, props.materialId),
});

/** 获取数据 */
async function loadData(data: any) {
  BaseInfoFormApi.setValues(data);
}

defineExpose({
  BaseInfoFormApi,
  Form,
  loadData,
});
</script>

<template>
  <Form v-loading="loading" />
</template>
