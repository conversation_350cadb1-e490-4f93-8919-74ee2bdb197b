<script lang="ts" setup>
import { computed, defineProps, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElTag } from 'element-plus';

import { useBaseInfoFormSchema } from './data';

const props = defineProps({
  formConfig: {
    default: () => {},
    type: Object,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  materialCode: {
    default: '',
    type: String,
  },
  materialId: {
    default: '',
    type: String,
  },
});

/** 基础表单 */
const [Form, BaseInfoFormApi] = useVbenForm({
  ...props.formConfig,
  wrapperClass: 'grid-cols-4',
  schema: useBaseInfoFormSchema(props.isView),
});

const baseInfoData = ref<any>({});

const isValid = computed(() => {
  return baseInfoData.value.isValid;
});

/** 获取数据 */
async function loadData(data: any) {
  baseInfoData.value = data;
  BaseInfoFormApi.setValues(data);
}

defineExpose({
  BaseInfoFormApi,
  Form,
  loadData,
});
</script>

<template>
  <Form>
    <template #currentVersion="props">
      <div>{{ props.modelValue }}</div>
      <ElTag class="ml-2" :type="isValid ? 'success' : 'danger'" effect="dark">
        {{ isValid ? '使用中' : '停用' }}
      </ElTag>
    </template>
  </Form>
</template>
