<script lang="ts" setup>
import { defineProps, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getCustomerProfile } from '#/api/base-info';

import { useProfileInfoFormSchema } from './data';

const props = defineProps({
  customerCode: {
    default: '',
    type: String,
  },
  customerId: {
    default: '',
    type: String,
  },
  formConfig: {
    default: () => {},
    type: Object,
  },
  isView: {
    default: false,
    type: Boolean,
  },
});

const loading = ref(false);

/** 表单 */
const [Form, FormApi] = useVbenForm({
  ...props.formConfig,
  schema: useProfileInfoFormSchema(props.isView),
});

// 表单数据
const formValue = ref();

/** 获取数据 */
async function loadData(customerId: string) {
  try {
    loading.value = true;
    const InfoFormRes = await getCustomerProfile({
      customerId,
    });

    formValue.value = InfoFormRes;

    FormApi.setValues(InfoFormRes);

    return InfoFormRes;
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}
onMounted(async () => {
  if (!isEmpty(props.customerId)) {
    loadData(props.customerId);
  }
});

defineExpose({
  Form,
  FormApi,
});
</script>

<template>
  <Form v-loading="loading" />
</template>
