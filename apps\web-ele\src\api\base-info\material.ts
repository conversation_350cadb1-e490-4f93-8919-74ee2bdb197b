import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath, rulePath, warehousePath } from '../path';

export namespace BaseDataMaterialApi {
  export interface BaseDataMaterial {
    [key: string]: any;
    pages: number;
    records: Array<{
      [key: string]: any;
    }>;
    total: number;
  }
}

/** 导出物料模板 */
export async function materialTemplate() {
  return requestClient.download(
    `${baseDataPath}/base/material/materialTemplate`,
  );
}

/** 导出物料模板 */
export async function BomTemplate() {
  return requestClient.download(`${baseDataPath}/base/material/BomTemplate`);
}

/** 导入物料信息*/
export async function importMaterial({
  data,
  isOverwrite,
}: {
  data: { file: Blob | File };
  isOverwrite: boolean;
}) {
  return requestClient.upload(
    `${baseDataPath}/base/material/importMaterial`,
    {
      ...data,
      isOverwrite,
    },

    {
      timeout: 60_000,
    },
  );
}

/** 导入生产BOM信息*/
export async function importProdBom(data: { file: Blob | File }) {
  return requestClient.upload(
    `${baseDataPath}/base/material/importProdBom`,
    data,
  );
}

/** 导出物料信息*/
export async function exportMaterial(params: Recordable<any>) {
  return requestClient.post<Blob>(
    `${baseDataPath}/base/material/exportMaterial`,
    {
      ...params,
    },
    {
      responseReturn: 'body',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 导出生产BOM信息*/
export async function exportProdBom(params: Recordable<any>) {
  return requestClient.post<Blob>(
    `${baseDataPath}/base/material/exportProdBom`,
    {
      ...params,
    },
    {
      responseReturn: 'body',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 启用物料*/
export async function enableMaterial(materialId: string) {
  return requestClient.get(
    `${baseDataPath}/base/material/enableMaterial/${materialId}`,
  );
}

/** 启用生产BOM*/
export async function enableProdBom(bomId: string) {
  return requestClient.get(
    `${baseDataPath}/base/material/prod/bom/enableProdBom/${bomId}`,
  );
}

/** 停用物料*/
export async function disableMaterial(materialId: string) {
  return requestClient.get(
    `${baseDataPath}/base/material/disableMaterial/${materialId}`,
  );
}

/** 停用BOM*/
export async function disableProdBom(bomId: string) {
  return requestClient.get(
    `${baseDataPath}/base/material/prod/bom/disableProdBom/${bomId}`,
  );
}

/** 将物料设为推荐*/
export async function recommendMaterial(materialId: string) {
  return requestClient.get(
    `${baseDataPath}/base/material/recommendMaterial/${materialId}`,
  );
}

/** 将物料设为不推荐*/
export async function noRecommendMaterial(materialId: string) {
  return requestClient.get(
    `${baseDataPath}/base/material/noRecommendMaterial/${materialId}`,
  );
}

/** 查询物料信息分页列表*/
export async function getMaterialPage(params: Recordable<any>) {
  return requestClient.post<BaseDataMaterialApi.BaseDataMaterial>(
    `${baseDataPath}/base/material/getMaterialPage`,
    { ...params },
  );
}

/** 查询母件信息分页列表*/
export async function getProdBomPage(params: Recordable<any>) {
  return requestClient.post<BaseDataMaterialApi.BaseDataMaterial>(
    `${baseDataPath}/base/material/getProdBomPage`,
    { ...params },
  );
}

/** 查询物料信息分页列表*/
export async function getMaterialBomPage(params: Recordable<any>) {
  return requestClient.post<BaseDataMaterialApi.BaseDataMaterial>(
    `${baseDataPath}/base/material/getMaterialBomPage`,
    { ...params },
  );
}

/** 查询物料细类树*/
export async function getMaterialCategoryTree(params?: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/material/category/getMaterialCategoryTree`,
    { ...params },
  );
}

/** 新增标准物料信息*/
export async function saveMaterial(params?: Recordable<any>) {
  return requestClient.post(`${baseDataPath}/base/material/saveMaterial`, {
    ...params,
  });
}

/** 新增非标准物料信息*/
export async function saveMaterialFalse(params?: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/material/nonstandard/saveMaterial`,
    {
      ...params,
    },
  );
}

/** 根据物料id或编号查询物料详细信息 */
export async function getMaterialDetail(data: {
  materialCode?: string;
  materialId?: string;
}) {
  const { materialCode, materialId } = data;
  return requestClient.get(
    `${baseDataPath}/base/material/getMaterialDetail?materialId=${materialId}&materialCode=${materialCode}`,
  );
}

/** 修改物料信息 */
export async function modMaterial(params?: Recordable<any>) {
  return requestClient.post(`${baseDataPath}/base/material/modMaterial`, {
    ...params,
  });
}

/** 新增生产BOM信息*/
export async function saveProdBom(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/material/prod/bom/save`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

/** 变更生产BOM信息*/
export async function updateProdBom(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/material/prod/bom/updateProdBom`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

/** 根据母件id查询物料的生产BOM信息 */
export async function getProdBomDetail(params?: Recordable<any>) {
  return requestClient.post(`${baseDataPath}/base/material/getProdBomDetail`, {
    ...params,
  });
}

/** 根据生产bomId查询物料的生产BOM信息 */
export async function getProdBomDetailById(data: { bomId: string }) {
  const { bomId } = data;
  return requestClient.get(
    `${baseDataPath}/base/material/getProdBomDetailById/${bomId}`,
  );
}

/** 导出子料清单模板 */
export async function ProdMaterielTemplate() {
  return requestClient.download(
    `${baseDataPath}/base/material/ProdMaterielTemplate`,
  );
}

/** 解析子料清单excel获取子料信息列表*/
export async function materielExcel(data: { file: Blob | File }) {
  return requestClient.upload(
    `${baseDataPath}/base/material/analyze/materielExcel`,
    data,
  );
}

/** 查询生产BOM的历史版本信息分页列表*/
export async function getProdBomHistoryPage(params?: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/material/getProdBomHistoryPage`,
    { ...params },
  );
}

/** 非标准物料批量转成标准物料 */
export async function changeMaterial(ids: string) {
  return requestClient.post(
    `${baseDataPath}/base/material/nonstandard/changeMaterial`,
    {
      ids,
    },
  );
}

/** 查询物料细类树*/
export async function getRuleList(params?: Recordable<any>) {
  return requestClient.post(`${rulePath}/rule/getRuleList`, { ...params });
}

/** 查询库位信息列表 */
export async function getLocationList() {
  return requestClient.post(`${warehousePath}/wm/location/getLocationList`);
}

/** 获取仓库列表(含库位) */
export async function getWarehouseList(params?: Recordable<any>) {
  return requestClient.post(`${warehousePath}/wm/warehouse/getWarehouseList`, {
    ...params,
  });
}
