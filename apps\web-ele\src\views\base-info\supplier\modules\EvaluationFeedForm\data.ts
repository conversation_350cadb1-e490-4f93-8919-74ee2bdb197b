import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { z } from '@girant/adapter';

import { getDictItemList } from '#/api';

const T_F_OPTIONS = [
  { label: '是', value: true },
  { label: '否', value: false },
];

export function useTransactionInfoFormSchema(
  isViewMode: boolean,
): VbenFormSchema[] {
  return [
    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: T_F_OPTIONS,
      },
      defaultValue: '',
      fieldName: 'isLicenseComplete',
      label: '证件是否齐全',
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: T_F_OPTIONS,
      },
      defaultValue: '',
      fieldName: 'isLicenseValid',
      label: '证件是否在有效期内',
      labelWidth: 140,
      rules: 'selectRequired',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: [], // 初始为空
      },
      defaultValue: '',
      dependencies: {
        async componentProps() {
          const data = await getDictItemList('baseSupplierFeedbackCertificate');
          return {
            options: data.map((item: any) => ({
              label: item.dictLabel,
              value: item.dictValue,
            })),
          };
        },
        triggerFields: [''],
      },
      fieldName: isViewMode ? 'certificateTypeLabel' : 'certificateType',
      formItemClass: 'col-start-1',
      label: '证件类型',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: [], // 初始为空
      },
      defaultValue: '',
      dependencies: {
        async componentProps() {
          const data = await getDictItemList('baseCertificationCertificate');
          return {
            options: data.map((item: any) => ({
              label: item.dictLabel,
              value: item.dictValue,
            })),
          };
        },
        triggerFields: [''],
      },
      fieldName: isViewMode
        ? 'certificationCertificateLabel'
        : 'certificationCertificate',
      formItemClass: 'col-start-1 col-span-full',
      label: '认证证书',
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'otherCertificate',
      formItemClass: 'col-start-1 col-span-full',
      label: '其他说明',

      suffix: () => {
        return isViewMode
          ? ''
          : h(
              'span',
              { class: 'text-nowrap' },
              '若选择其他认证证书需填写其他说明',
            );
      },
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'RadioGroup',
      componentProps: {
        options: T_F_OPTIONS,
      },
      defaultValue: '',
      fieldName: 'isSiteAudited',
      formItemClass: 'col-start-1',
      label: '是否进行现场审查',
      labelWidth: 130,
    },

    {
      component: isViewMode
        ? (props: any) => {
            const val = props.modelValue;
            const showText = val || '/';
            return h('div', null, showText);
          }
        : 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.evaluationOpinion) {
            return z.string().max(1000, { message: '最多输入1000个字符' });
          }
          return null;
        },
        triggerFields: ['evaluationOpinion'],
      },
      fieldName: 'evaluationOpinion',
      formItemClass: 'col-span-full items-start',
      label: '评估意见',
    },
  ];
}
