<script lang="ts" setup>
import { defineProps } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { useBaseInfoFormSchema } from './data';

const props = defineProps({
  formConfig: {
    default: () => {},
    type: Object,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  materialCode: {
    default: '',
    type: String,
  },
  materialId: {
    default: '',
    type: String,
  },
});

/** 基础资料表单 */
const [Form, BaseInfoFormApi] = useVbenForm({
  ...props.formConfig,
  schema: useBaseInfoFormSchema(props.isView, props.materialId),
});

const getFormValues = async () => {
  const formValues = await BaseInfoFormApi.getValues();
  return formValues;
};

const validateForm = async () => {
  const validatRes = await BaseInfoFormApi.validate();
  return validatRes;
};

// 统一提交处理
const onSubmitToBaseInfoForm = async () => {
  const { valid } = await validateForm();
  if (!valid) {
    throw new Error('物料信息表单验证失败');
  }

  const serialNumber: any =
    await BaseInfoFormApi?.getFieldComponentRef('serialNumber');
  const isCompleted_SerialNumber = await serialNumber.getCompleteStatus();
  if (!isCompleted_SerialNumber) {
    ElMessage.error('等待附件上传完成');
    return;
  }

  const mergedValues = await getFormValues();

  return mergedValues;
};

/** 获取数据 */
async function loadData(data: any) {
  BaseInfoFormApi.setValues(data);
}

defineExpose({
  BaseInfoFormApi,
  Form,
  loadData,
  onSubmitToBaseInfoForm,
});
</script>

<template>
  <Form />
</template>
