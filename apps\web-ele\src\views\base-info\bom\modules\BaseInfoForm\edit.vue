<script lang="ts" setup>
import { computed, defineProps, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { useVbenForm } from '@girant/adapter';

import { useBaseInfoFormSchema } from './data';

const props = defineProps({
  formConfig: {
    default: () => {},
    type: Object,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  materialCode: {
    default: '',
    type: String,
  },
  materialId: {
    default: '',
    type: String,
  },
  isEdit: {
    default: false,
    type: Boolean,
  },
});

const emits = defineEmits(['selMaterialId']);

const onChangeSel = (materialId: string) => {
  emits('selMaterialId', materialId);
};

/** 基础表单 */
const [Form, BaseInfoFormApi] = useVbenForm({
  ...props.formConfig,
  schema: useBaseInfoFormSchema(props.isView, props.isEdit, onChangeSel),
});

const getFormValues = async () => {
  const formValues = await BaseInfoFormApi.getValues();
  return formValues;
};

const validateForm = async () => {
  const validatRes = await BaseInfoFormApi.validate();
  return validatRes;
};

// 统一提交处理
const onSubmitToBaseInfoForm = async () => {
  const { valid } = await validateForm();
  if (!valid) {
    throw new Error('BOM信息表单验证失败');
  }
  const mergedValues = await getFormValues();

  if (isEmpty(props.materialId)) {
    return mergedValues;
  } else {
    delete mergedValues.materialName;
    return { ...mergedValues, materialId: props.materialId };
  }
};

const baseInfoData = ref<any>({});

const latestVersion = computed(() => {
  return baseInfoData.value.latestVersion;
});

/** 获取数据 */
async function loadData(data: any) {
  baseInfoData.value = data;
  BaseInfoFormApi.setValues(data);
}

defineExpose({
  BaseInfoFormApi,
  Form,
  loadData,
  onSubmitToBaseInfoForm,
});
</script>

<template>
  <Form>
    <template #version="props">
      <el-input v-bind="props" />
      <span
        v-if="latestVersion"
        class="ml-2 whitespace-nowrap text-sm text-gray-500"
      >
        当前最新版本:{{ latestVersion }}
      </span>
    </template>
  </Form>
</template>
