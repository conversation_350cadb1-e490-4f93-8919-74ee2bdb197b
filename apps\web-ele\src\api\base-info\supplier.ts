import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath, systemPath } from '../path';

export namespace BaseDataSupplierApi {
  export interface BaseDataSupplier {
    [key: string]: any;
    records: Array<{
      [key: string]: any;
      tagList: Array<{ [key: string]: any }>;
    }>;
    total: string;
  }
}

/** 模板下载 */
export async function supplierTemplate() {
  return requestClient.download(
    `${baseDataPath}/base/supplier/supplierTemplate`,
  );
}

/** 导入*/
export async function importSupplier(data: { file: Blob | File }) {
  return requestClient.upload(
    `${baseDataPath}/base/supplier/importSupplier`,
    data,
  );
}

/** 列表导出*/
export async function exportSupplier(params: Recordable<any>) {
  return requestClient.post<Blob>(
    `${baseDataPath}/base/supplier/exportSupplier`,
    {
      ...params,
    },
    {
      responseReturn: 'body',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 获取分页列表*/
export async function getSupplierPageList(params: Recordable<any>) {
  return requestClient.post<Array<BaseDataSupplierApi.BaseDataSupplier>>(
    `${baseDataPath}/base/supplier/getSupplierPage`,
    { ...params },
  );
}

/** 新增供应商*/
export async function saveSupplier(
  params: Omit<BaseDataSupplierApi.BaseDataSupplier, 'supplierId'>,
) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/saveSupplier`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

/** 根据供应商id停用供应商*/
export async function disableSupplier(supplierId: string) {
  return requestClient.get(
    `${baseDataPath}/base/supplier/disableSupplier/${supplierId}`,
  );
}

/** 根据供应商id启用供应商*/
export async function enableSupplier(supplierId: string) {
  return requestClient.get(
    `${baseDataPath}/base/supplier/enableSupplier/${supplierId}`,
  );
}

// --------基本信息---------
/** 根据供应商id或编号获取供应商基本信息*/
export async function getSupplierInfo(data: {
  supplierCode?: string;
  supplierId?: string;
}) {
  const { supplierCode, supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getSupplierInfo?supplierId=${supplierId}&supplierCode=${supplierCode}`,
  );
}

// /** 修改供应商基本信息*/
export async function updateSupplierBaseInfo(data: any) {
  return requestClient.post(`${baseDataPath}/base/supplier/modSupplier`, data);
}

// --------标签信息---------
/** 根据供应商id查询供应商标签列表 */
export async function getSupplierTag(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getSupplierTag/${supplierId}`,
  );
}

/** 修改供应商标签列表 */
export async function modSupplierTag(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/tag/modSupplierTag`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------联系人信息---------
/** 根据供应商id获取供应商联系人列表 */
export async function getContactsList(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getContactsList/${supplierId}`,
  );
}

/** 修改联系人列表 */
export async function modSupplierContacts(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/contacts/modSupplierContacts`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------公司概况---------
/** 根据供应商id获取供应商概况信息*/
export async function getProfile(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getProfile/${supplierId}`,
  );
}

/** 修改供应商概况信息 */
export async function modSupplierProfile(data: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/profile/modSupplierProfile`,
    data,
  );
}

// --------主要负责人信息---------
/** 根据供应商id获取供应商负责人列表 */
export async function getResponsibleList(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getResponsibleList/${supplierId}`,
  );
}

/** 修改供应商负责人列表 */
export async function modSupplierResponsible(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/responsible/modSupplierResponsible`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------供应物料---------
/** 根据供应商id获取主要物料供应列表 */
export async function getMaterialList(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getMaterialList/${supplierId}`,
  );
}

/** 修改供应商供应物料列表 */
export async function modSupplierMaterial(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/material/modSupplierMaterial`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------交易信息---------
/** 根据供应商id获取供应商交易记录信息 */
export async function getTransaction(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getTransaction/${supplierId}`,
  );
}

/** 修改供应商交易记录信息 */
export async function modSupplierTransaction(data: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/transaction/modSupplierTransaction`,
    data,
  );
}

// --------年度总销售额---------
/** 根据供应商id获取供应商年度总销售额列表 */
export async function getSalesInfoList(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getSalesInfoList/${supplierId}`,
  );
}

/** 修改供应年度总销售额列表 */
export async function modSupplierSalesInfo(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/salesInfo/modSupplierSalesInfo`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------主要销售供应商---------
/** 根据供应商id获取供应商主要销售供应商列表 */
export async function getCustomerSalesList(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getCustomerSalesList/${supplierId}`,
  );
}

/** 修改供应年度总销售额列表 */
export async function modSupplierCustomerSales(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/customerSales/modSupplierCustomerSales`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------主要原料供应商---------
/** 根据供应商id获取供应商的主要原料供应商列表 */
export async function getSubSupplierList(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getSubSupplierList/${supplierId}`,
  );
}

/** 修改供应年度总销售额列表 */
export async function modSupplierSubSupplier(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/subSupplier/modSupplierSubSupplier`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------供应商评价意见---------
/** 根据供应商id获取供应商交易记录信息 */
export async function getEvaluationFeed(data: { supplierId: string }) {
  const { supplierId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/getEvaluationFeed/${supplierId}`,
  );
}

/** 修改供应商交易记录信息 */
export async function modSupplierEvaluationFeed(data: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/evaluationFeed/modSupplierEvaluationFeed`,
    data,
  );
}

// --------供应商采购框架合同信息---------
/** 根据供应商id获取供应商采购框架合同信息 */
export async function getFrameworkList(data: { supplierId: string }) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/getFrameworkList`,
    data,
  );
}

/** 修改供应商采购框架合同信息 */
export async function modSupplierFramework(data: any) {
  return requestClient.post(
    `${baseDataPath}/base/supplier/framework/modSupplierFramework`,
    data,
  );
}

// 合同作废
export async function invalid(data: { contractId: string }) {
  const { contractId } = data;
  return requestClient.get(
    `${baseDataPath}/base/supplier/invalid/${contractId}`,
  );
}

/** 获取用户类型枚举列表 */
export async function getUserTypeList() {
  return requestClient.get(`${systemPath}/user/getUserTypeList`);
}
