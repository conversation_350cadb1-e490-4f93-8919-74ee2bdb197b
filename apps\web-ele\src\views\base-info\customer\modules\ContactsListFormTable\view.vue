<script lang="ts" setup>
import type { RowType } from './data';

import { defineProps, nextTick, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getCustomerContacts } from '#/api/base-info';

import { useViewGridOptions } from './data';

const props = defineProps({
  customerCode: {
    default: '',
    type: String,
  },
  customerId: {
    default: '',
    type: String,
  },
  isView: {
    default: false,
    type: Boolean,
  },
});

const loading = ref(false);
const gridTableData = ref<Array<RowType>>([]);

// 预览表格
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions: useViewGridOptions() });

/** 获取数据 */
async function loadData(customerId: string) {
  try {
    loading.value = true;
    const FormTableRes = await getCustomerContacts({
      customerId,
    });

    if (!FormTableRes) {
      return;
    }

    gridTableData.value = FormTableRes;

    nextTick(() => {
      gridApi.setGridOptions({
        data: gridTableData.value,
      });
    });

    return gridTableData.value;
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  if (!isEmpty(props.customerId)) {
    loadData(props.customerId);
  }
});
</script>

<template>
  <div v-loading="loading">
    <Grid />
  </div>
</template>
