import { computed, defineComponent, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import Edit from './edit.vue';
import View from './view.vue';

const EvaluationFeedForm = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    View,
  },
  name: 'EvaluationFeedForm',
  props: {
    formConfig: {
      default: () => ({}),
      type: Object,
    },
    isView: {
      default: false,
      type: <PERSON>olean,
    },
    supplierCode: {
      default: '',
      type: String,
    },
    supplierId: {
      default: '',
      type: String,
    },
  },
  setup(props, { expose }) {
    const isEdit = computed(() => !isEmpty(props.supplierId));
    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();

    const getEvaluationFeedFormValues = async () => {
      if (componentRef.value?.onSubmitToForm) {
        return await componentRef.value.onSubmitToForm();
      }
      return null;
    };

    const handleEdit = async () => {
      componentRef.value.onSubmitToForm();
    };

    expose({
      getEvaluationFeedFormValues,
    });

    return {
      componentRef,
      currentComponent,
      handleEdit,
      isEdit,
    };
  },
  template: `
    <FormCard title="供应商评价意见">
      <template #titleMore v-if="isEdit && !isView">
        <ElButton type="primary" @click="handleEdit" size="small">修改</ElButton>
      </template>
      <component
        ref="componentRef"
        :is="currentComponent"
        :supplierCode="supplierCode"
        :supplierId="supplierId"
        :formConfig="formConfig"
        :isView="isView"
        :isEdit="isEdit"
       
      />
    </FormCard>
  `,
});

export { EvaluationFeedForm };
