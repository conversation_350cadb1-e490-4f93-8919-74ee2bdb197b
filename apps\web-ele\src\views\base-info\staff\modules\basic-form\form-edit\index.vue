<script setup lang="ts">
import type { CascaderProps } from 'element-plus';

import { ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElCascader } from 'element-plus';

import { getAreaList } from '#/api/base-info';

import { useFormSchema } from './data';

/** 地区选择 */
const areaOptions = ref([]);
/** 基础资料表单 */
const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass:
    'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 ',
});

/** 动态加载地区级联 */
const cascaderPanelProps: CascaderProps = {
  checkStrictly: true,
  label: 'areaName',
  lazy: true,
  async lazyLoad(node, resolve) {
    const { level, value } = node;
    // 获取下一级数据 level是从0开始的，但是接口是从1开始的 所以level+1
    const res = await getAreaList((level + 1).toString(), value as string);
    // 如果没有数据，设置为叶子节点
    if (!res) {
      // 设置为叶子节点
      node.data!.leaf = true;
    }
    // 如果是第三级，直接设置为叶子节点
    if (level === 2) {
      res?.forEach((item: any) => {
        item.leaf = true;
      });
    }
    resolve(res);
  },
  multiple: false,
  value: 'areaCode',
};

defineExpose({
  areaOptions, // 地区
  formApi,
});
</script>

<template>
  <Form>
    <template #placeOrigin>
      <ElCascader
        class="w-full"
        :props="cascaderPanelProps"
        v-model="areaOptions"
        clearable
      />
    </template>
  </Form>
</template>
