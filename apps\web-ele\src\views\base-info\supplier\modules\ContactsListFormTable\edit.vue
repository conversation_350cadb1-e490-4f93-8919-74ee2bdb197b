<script lang="ts" setup>
import type { OnActionClickParams } from '@girant/adapter';

import type { RowType } from './data';

import { defineProps, nextTick, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { ElMessage, ElMessageBox } from 'element-plus';

import { getContactsList, modSupplierContacts } from '#/api/base-info';

import { useGridOptions } from './data';

const props = defineProps({
  isEdit: {
    default: false,
    type: Boolean,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  supplierCode: {
    default: '',
    type: String,
  },
  supplierId: {
    default: '',
    type: String,
  },
});

const loading = ref(false);
const gridTable = ref();
const gridTableData = ref<Array<RowType>>([]);

function onActionClick(e: OnActionClickParams) {
  switch (e.code) {
    case 'cancel': {
      gridTable.value.revertRow(e.row);
      gridTable.value.cancelRow(e.row);
      break;
    }
    case 'delete': {
      gridTable.value.removeRow(e.row);
      break;
    }
    case 'edit': {
      gridTable.value.editRow(e.row);
      break;
    }
  }
}
// 编辑表单表格
const gridOptions = useGridOptions(onActionClick);

/** 获取数据 */
async function loadData(supplierId: string) {
  try {
    loading.value = true;
    const FormTableRes = await getContactsList({
      supplierId,
    });

    if (!FormTableRes) {
      return;
    }

    gridTableData.value = FormTableRes;

    nextTick(() => {
      gridTable.value.setTableData(gridTableData.value);
    });

    return gridTableData.value;
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  nextTick(() => {
    if (!props.isView) {
      gridTable.value.setTableData([]);
    }
  });

  if (!isEmpty(props.supplierId)) {
    loadData(props.supplierId);
  }
});

// 获取表格数据
const getTableData = async () => {
  const tableData = await gridTable.value.getTableData();
  return tableData;
};

// 校验表格数据
const tableValidate = async () => {
  const tableValidateRes = await gridTable.value.tableValidate(true);
  return tableValidateRes;
};

// 判断是否有多个默认联系人
const multipleIsDefault = (tableData: Array<RowType>) => {
  return tableData.filter((item) => item.isDefault).length > 1;
};

const onSubmitToFormTable = async () => {
  const mergedValues: {
    contactsList: Array<RowType>;
    supplierId?: string;
  } = {
    contactsList: [],
  };
  try {
    loading.value = true;
    const tableData = await getTableData();

    // 空表单检查 - 仅在新建模式下
    if (tableData.length === 0) {
      return null;
    }

    // 表单验证
    const tableValidateRes = await tableValidate();
    if (tableValidateRes) {
      if (props.isEdit) {
        // 编辑模式：显示错误消息
        ElMessage.error('请正确填写表单');
        return null;
      } else {
        // 新建模式：抛出错误给调用方
        throw new Error('联系人信息表单验证失败');
      }
    }

    if (multipleIsDefault(tableData)) {
      if (props.isEdit) {
        ElMessage.warning('默认联系人只能有一个');
        return;
      } else {
        throw new Error('默认联系人只能有一个');
      }
    }

    // 编辑模式
    if (props.isEdit) {
      mergedValues.supplierId = props.supplierId;
      mergedValues.contactsList = tableData;
      if (
        await ElMessageBox.confirm('确定修改吗？', '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning',
        })
      ) {
        await modSupplierContacts(mergedValues);
        ElMessage.success('编辑成功');
        loadData(props.supplierId);
      }
      return null;
    }

    // 新建模式
    return tableData;
  } catch (error) {
    if (!props.isEdit && error instanceof Error) {
      // 新建模式下，将错误继续向上抛出
      throw error;
    } else {
      // 编辑模式错误处理
      ElMessage.error('编辑失败');
      console.error(error);
      return null;
    }
  } finally {
    loading.value = false;
  }
};

defineExpose({
  onSubmitToFormTable,
});
</script>

<template>
  <div v-loading="loading">
    <DynamicTable ref="gridTable" :grid-options="gridOptions" class="border" />
  </div>
</template>
