import { computed, defineComponent, ref } from 'vue';

import { ElButton } from 'element-plus';

import FormCard from '#/components/form-card/Index.vue';

import Edit from './edit.vue';
import View from './view.vue';

const Purchase = defineComponent({
  components: {
    FormCard,
    Edit,
    ElButton,
    View,
  },
  name: 'Purchase',
  props: {
    formConfig: {
      default: () => ({}),
      type: Object,
    },
    isEdit: {
      default: false,
      type: Boolean,
    },
    isView: {
      default: false,
      type: Boolean,
    },
    materialCode: {
      default: '',
      type: String,
    },
    materialId: {
      default: '',
      type: String,
    },
  },
  setup(props, { expose }) {
    const currentComponent = computed(() => (props.isView ? View : Edit));
    const componentRef = ref();

    const getPurchaseValues = async () => {
      if (componentRef.value?.onSubmitToPurchase) {
        return await componentRef.value.onSubmitToPurchase();
      }
      return null;
    };

    const loadData = (data: any) => {
      componentRef.value.loadData(data);
    };

    expose({
      getPurchaseValues,
      loadData,
    });

    return {
      componentRef,
      currentComponent,
    };
  },
  template: `
    <FormCard title="采购配置">
      <component
        ref="componentRef"
        :is="currentComponent"
        :materialCode="materialCode"
        :materialId="materialId"
        :formConfig="formConfig"
        :isView="isView"
        :isEdit="isEdit"
      />
    </FormCard>
  `,
});

export { Purchase };
