<script lang="ts" setup>
import { defineProps, ref } from 'vue';

import { AccessControl, useAccess } from '@vben/access';

import { ElMessage, ElMessageBox } from 'element-plus';

import { createCustomer } from '#/api/base-info';

import { BaseInfoForm } from './BaseInfoForm/index';
import { ContactsListFormTable } from './ContactsListFormTable/index';
import { ProfileInfoForm } from './ProfileInfoForm/index';
import { ResponsibleListFormTable } from './ResponsibleListFormTable/index';
import { TagListFormTable } from './TagListFormTable';
import { TransactionInfoForm } from './TransactionInfoForm/index';

const props = defineProps({
  customerCode: {
    default: '',
    type: String,
  },
  customerId: {
    default: '',
    type: String,
  },
  isView: {
    default: false,
    type: Boolean,
  },
});

const emits = defineEmits(['isSubmit', 'submitState']);

const { hasAccessByCodes } = useAccess();

const getPermissionCodes = (module: string) => {
  if (props.customerId) {
    const queryCode = `base:customer:query:${module}`;
    const editCode = `base:customer:edit:modify:${module}`;
    return [queryCode, props.isView ? '' : editCode];
  } else {
    return ['base:customer:edit:add'];
  }
};

/** 合并公共表单配置*/
const formConfig = {
  commonConfig: {
    componentProps: { class: 'w-full' },
    hideRequiredMark: props.isView,
  },

  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
};

/** 基础资料 */
const BaseInfoFormRef = ref();
/** 客户标签信息 */
const TagListFormTableRef = ref();
/** 联系人信息 */
const ContactsListFormTableRef = ref();
/** 公司概况 */
const ProfileInfoFormRef = ref();
/** 负责人 */
const ResponsibleListFormTableRef = ref();
/** 交易信息 */
const TransactionInfoFormRef = ref();

type AsyncTask<T> = {
  fn: () => Promise<null | T>;
  key: string;
};

type FormData = {
  contactsList?: unknown;
  customerInfo?: unknown;
  profileInfo?: unknown;
  responsibleList?: unknown;
  tagList?: unknown;
  transactionInfo?: unknown;
};

const submitAllForm = async () => {
  if (
    !(await ElMessageBox.confirm('确定提交吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    }))
  ) {
    return;
  }

  const tasks: AsyncTask<unknown>[] = [
    { fn: BaseInfoFormRef.value.getBaseInfoFormValues, key: 'customerInfo' },
    {
      fn: TagListFormTableRef.value.getTagListFormTableValues,
      key: 'tagList',
    },
    {
      fn: ContactsListFormTableRef.value.getContactsListFormTableValues,
      key: 'contactsList',
    },
    {
      fn: ProfileInfoFormRef.value.getProfileInfoFormValues,
      key: 'profileInfo',
    },
    {
      fn: ResponsibleListFormTableRef.value.getResponsibleListFormTableValues,
      key: 'responsibleList',
    },
    {
      fn: TransactionInfoFormRef.value.getTransactionInfoFormValues,
      key: 'transactionInfo',
    },
  ];

  const validData: FormData = {};

  try {
    const results = await Promise.all(tasks.map((task) => task.fn()));

    for (const [index, task] of tasks.entries()) {
      const value = results[index];
      if (value !== null && value !== undefined) {
        validData[task.key as keyof FormData] = value;
      }
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : '表单获取失败，请检查表单数据';
    ElMessage.error(errorMessage);
  }

  try {
    emits('isSubmit', true);

    await createCustomer(validData);
    emits('isSubmit', false);
    emits('submitState', true);
  } catch {
    emits('isSubmit', false);
  }
};

const showHandle = (module: string) => {
  // 新增模式：可编辑
  if (!props.customerId) {
    return false;
  }

  // 查看模式：只读
  if (props.isView) {
    return true;
  }

  // 编辑模式：根据权限决定是否只读
  return !hasAccessByCodes([`base:customer:edit:modify:${module}`]);
};

defineExpose({ submitAllForm });
</script>

<template>
  <div>
    <AccessControl :codes="getPermissionCodes('basic')" type="code">
      <BaseInfoForm
        ref="BaseInfoFormRef"
        :is-view="showHandle('basic')"
        :form-config="formConfig"
        :customer-id="customerId"
        :customer-code="customerCode"
        @base-info-submit-state="(val: boolean) => emits('submitState', val)"
      />
    </AccessControl>

    <AccessControl :codes="getPermissionCodes('tag')" type="code">
      <TagListFormTable
        ref="TagListFormTableRef"
        :is-view="showHandle('tag')"
        :customer-id="customerId"
        :customer-code="customerCode"
      />
    </AccessControl>

    <AccessControl :codes="getPermissionCodes('contacts')" type="code">
      <ContactsListFormTable
        ref="ContactsListFormTableRef"
        :is-view="showHandle('contacts')"
        :customer-id="customerId"
        :customer-code="customerCode"
      />
    </AccessControl>

    <AccessControl :codes="getPermissionCodes('profile')" type="code">
      <ProfileInfoForm
        ref="ProfileInfoFormRef"
        :is-view="showHandle('profile')"
        :form-config="formConfig"
        :customer-id="customerId"
        :customer-code="customerCode"
      />
    </AccessControl>

    <AccessControl :codes="getPermissionCodes('leader')" type="code">
      <ResponsibleListFormTable
        ref="ResponsibleListFormTableRef"
        :is-view="showHandle('leader')"
        :customer-id="customerId"
        :customer-code="customerCode"
      />
    </AccessControl>

    <AccessControl :codes="getPermissionCodes('trading')" type="code">
      <TransactionInfoForm
        ref="TransactionInfoFormRef"
        :is-view="showHandle('trading')"
        :form-config="formConfig"
        :customer-id="customerId"
        :customer-code="customerCode"
      />
    </AccessControl>
  </div>
</template>
