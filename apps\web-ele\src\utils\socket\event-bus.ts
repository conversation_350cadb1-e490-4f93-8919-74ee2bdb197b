// event-bus.ts
class EventBus {
  private events: { [key: string]: ((data: any) => void)[] } = {};

  // 发布事件
  emit(eventName: string, data: any) {
    if (this.events[eventName]) {
      this.events[eventName].forEach((callback) => callback(data));
    }
  }

  // 取消订阅事件
  off(eventName: string, callback: (data: any) => void) {
    if (this.events[eventName]) {
      this.events[eventName] = this.events[eventName].filter(
        (cb) => cb !== callback,
      );
    }
  }

  // 订阅事件
  on(eventName: string, callback: (data: any) => void) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(callback);
  }
}

// 创建事件总线实例
const eventBus = new EventBus();

export default eventBus;
