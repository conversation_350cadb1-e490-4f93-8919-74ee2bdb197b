import { ref } from 'vue';

import { useAppConfig } from '@vben/hooks';
import { useAccessStore } from '@vben/stores';

import { ElMessage } from 'element-plus';

import eventBus from './event-bus';

const { wsURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

const accessToken = useAccessStore().accessToken;
// WebSocket 实例
const socket = ref<null | WebSocket>(null);
// 连接状态Promise
let socketReadyPromise: null | Promise<void> = null;
let resolveSocketReady: ((value: PromiseLike<void> | void) => void) | null =
  null;
// 心跳定时器
let heartbeatInterval: null | ReturnType<typeof setInterval> = null;

// 最大重连次数
const MAX_RECONNECT_ATTEMPTS = 5;
// 当前重连次数
let reconnectAttempts = 0;

const createSocket = () => {
  const wsUrl = new URL(`${wsURL}/ws`);
  if (accessToken) {
    wsUrl.searchParams.append('token', accessToken);
  }

  return new WebSocket(wsUrl.href);
};

const setupSocketEvents = () => {
  if (!socket.value) return;

  // 初始化Promise
  socketReadyPromise = new Promise((resolve) => {
    resolveSocketReady = resolve;
  });

  // 连接成功事件
  socket.value.addEventListener('open', () => {
    resolveSocketReady?.(); // 标记连接已就绪

    // 启动心跳定时器
    heartbeatInterval = setInterval(() => {
      sendMessage({ type: 'PING', timestamp: Date.now() });
    }, 30_000);
  });

  // 消息接收事件
  socket.value.addEventListener('message', (event) => {
    try {
      const data = JSON.parse(event.data);
      // 使用事件总线发布消息
      eventBus.emit(data.topic, data);
    } catch (error) {
      console.error('解析消息出错:', error);
    }
  });

  // 连接关闭事件
  socket.value.addEventListener('close', (event) => {
    console.error('WebSocket 连接已关闭', event.code, event.reason);

    // 清除心跳定时器
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }

    // 重连逻辑
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++;
      console.error(`尝试第 ${reconnectAttempts} 次重连...`);
      setTimeout(() => {
        connectSocket();
      }, 5000); // 5秒后尝试重连
    } else {
      console.error('已达到最大重连次数，停止重连');
    }
  });

  // 错误处理事件
  socket.value.addEventListener('error', (error) => {
    console.error('WebSocket 发生错误:', error);
    ElMessage.error('WebSocket连接错误');
  });
};

export const connectSocket = () => {
  if (socket.value && socket.value.readyState === WebSocket.OPEN) {
    return socketReadyPromise;
  }

  if (!accessToken) {
    ElMessage.error('请登录系统');
    return Promise.reject(new Error('未登录'));
  }

  try {
    socket.value = createSocket();
    setupSocketEvents();
    console.warn('创建WebSocket连接成功');
    return socketReadyPromise || Promise.resolve();
  } catch (error: any) {
    console.error('创建WebSocket连接失败:', error);
    ElMessage.error(`创建WebSocket连接失败: ${error.message}`);
    return Promise.reject(error);
  }
};

const sendMessage = async (message: any) => {
  try {
    // 等待连接就绪
    await socketReadyPromise;

    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
      socket.value.send(JSON.stringify(message));
    } else {
      throw new Error('WebSocket连接未就绪');
    }
  } catch (error) {
    console.error('发送消息失败:', error);
    ElMessage.error('消息发送失败');
    throw error;
  }
};

export const subscribeTopic = async (topic: string) => {
  if (!topic) {
    ElMessage.error('请输入消息主题');
    return;
  }

  await sendMessage({
    type: 'SUBSCRIBE',
    topic,
  });
};

export const unsubscribeTopic = async (topic: string) => {
  if (!topic) {
    ElMessage.error('请输入消息主题');
    return;
  }

  await sendMessage({
    type: 'UNSUBSCRIBE',
    topic,
  });
};

export const disconnectSocket = () => {
  if (socket.value) {
    socket.value.close();
    socket.value = null;
    ElMessage.error('WebSocket 连接已主动关闭');
  }
};

// 自动尝试连接
connectSocket();

export const WS = {
  connectSocket,
  disconnectSocket,
  subscribeTopic,
  unsubscribeTopic,
  on: eventBus.on.bind(eventBus),
  off: eventBus.off.bind(eventBus),
  emit: eventBus.emit.bind(eventBus),
};
