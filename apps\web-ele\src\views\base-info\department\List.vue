<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';
import type { UploadRequestOptions } from 'element-plus';

import type { Recordable } from '@vben/types';

import type { BaseDataDeptApi } from '#/api/base-info';

import { nextTick, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElMessage,
  ElMessageBox,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import {
  DeptTemplate,
  disableDept,
  enableDept,
  exportDept,
  getAllDeptTree,
  importDept,
} from '#/api/base-info';

import { useGridFormSchema, useTableFormSchema } from './data';
import FormEdit from './modules/form-edit/index.vue';
import FormView from './modules/form-view/index.vue';

/** 模态框表单 */
const modalFormRef = ref();
/** 部门id */
const deptId = ref<string>('');
/** 是否是查看状态*/
const isView = ref(false);
/** 操作 */
const onActionClick = (
  e: OnActionClickParams<BaseDataDeptApi.BaseDataDept>,
) => {
  switch (e.code) {
    case 'edit': {
      onAction(e.row.deptId, false, '编辑');
      break;
    }
    case 'view': {
      onAction(e.row.deptId, true, '查看');
      break;
    }
  }
};
/** 切换启用状态 */
const onStatusChange = async (
  newStatus: Boolean,
  row: BaseDataDeptApi.BaseDataDept,
) => {
  const isEnable: Recordable<string> = {
    false: '已禁用',
    true: '已启用',
  };
  try {
    await confirm(
      `您要将 【${row.deptName}】 的状态切换为 【${isEnable[newStatus.toString()]}】 吗？`,
      `切换状态`,
    );
    if (row.isEnable) {
      const res = await disableDept(row.deptId);
      const isSuccess = await DisableProcessing(row, res);
      if (isSuccess && res.data.notDisableSonDeptCount > 0) {
        // 刷新表格
        await gridApi.query();
      }
      return isSuccess;
    } else {
      await enableDept(row.deptId);
      ElMessage.success('启用成功');
      return true;
    }
  } catch {
    return false;
  }
};

// 查询表单
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[70px]',
    },
    schema: useGridFormSchema(),
    showCollapseButton: false,
    wrapperClass:
      'grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  },
  gridOptions: {
    border: true,
    columns: useTableFormSchema(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    // 由于设置enabled: false, 导致数据显示不出来,可能是我使用了proxyConfig的原因，所以折中设置autoHidden来隐藏
    pagerConfig: {
      autoHidden: true, // 当只有一页时隐藏分页器
      enabled: true,
      pageSize: 999_999, // 设置每页大小
    },
    proxyConfig: {
      ajax: {
        // eslint-disable-next-line no-empty-pattern
        query: async ({}, formValues: Recordable<any>) => {
          // 获取部门树
          const response = await getAllDeptTree({
            ...formValues,
          });
          return {
            result: response,
          };
        },
        querySuccess: async () => {
          // 获取数据后，展开所有节点
          await nextTick();
          gridApi.grid.setAllTreeExpand(true);
        },
      },
      response: {
        result: 'result',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {},
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
    treeConfig: {
      rowField: 'positionCode',
    },
  } as VxeTableGridOptions<BaseDataDeptApi.BaseDataDept>,
});

// 模态框
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    modalFormRef.value.formApi.validateAndSubmitForm();
  },
  showCancelButton: true,
  showConfirmButton: true,
});

const confirm = (content: string, title: string) => {
  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {
        reject(new Error('已取消'));
      });
  });
};

/**
 * 根据停用返回的结果做出不同的操作
 * @param row 停用的部门信息
 * @param data 停用的结果
 * @returns boolean 是否停用成功
 */
const DisableProcessing = async (row: any, data: any) => {
  if (data.code === 10_002_000) {
    ElMessage.success(
      data.data.notDisableSonDeptCount
        ? `已成功停用【${row.deptName}】部门【和各级子部门${data.data.notDisableSonDeptCount}个】`
        : `已成功停用【${row.deptName}】部门`,
    );
    return true;
  }
  // 10_005_010  判断是否存在未停用的子部门或岗位或员工
  else if (data.code === 10_005_010) {
    // 存在未停用的岗位或员工，则提示用户不可停用
    if (
      data.data.notDisablePositionCount > 0 ||
      data.data.notDisableStaffCount > 0
    ) {
      let text = `不可停用，因为当前部门下存在：`;
      if (data.data.notDisableSonDeptCount > 0) {
        text = `${text}【${data.data.notDisableSonDeptCount}个未停用子部门】`;
      }
      if (data.data.notDisablePositionCount > 0) {
        text = `${text}【${data.data.notDisablePositionCount}个未停用岗位】`;
      }
      if (data.data.notDisableStaffCount > 0) {
        text = `${text}【${data.data.notDisableStaffCount}个未停用员工】`;
      }
      await confirm(`${text}`, '提示');
      return false;
    }
    // 不存在关联的未停用岗位或未停用的员工 则提示用户是否一起停用
    else {
      await confirm(
        `当前部门下存在【${data.data.notDisableSonDeptCount}个未停用部门】，是否确定一起停用？`,
        '停用当前部门和其子部门',
      );
      const res = await disableDept(row.deptId, true);
      // 检查停用结果
      await DisableProcessing(row, res);
      return true;
    }
  } else {
    ElMessage.error('停用失败');
    return false;
  }
};

/**
 * 表格操作
 * @param id
 * @param view 是否是查看状态
 * @param title 标题
 */
const onAction = (
  id: string = '',
  view: boolean = false,
  title: string = '操作',
) => {
  // 这里根据需求进行修改
  deptId.value = id;
  isView.value = view;
  formModalApi
    .setState({
      showConfirmButton: !view,
      title,
    })
    .open();
};

/** 关闭 */
const FormModalClose = async () => {
  formModalApi.close();
  await gridApi.query();
};

/** 下载模板 */
const getDeptTemplate = async () => {
  try {
    const response = await DeptTemplate();
    downloadFileFromResponse(response);
  } catch (error) {
    console.error('文件下载失败：', error);
  }
};

/** 数据导入*/
const deptImportHandle = async (options: UploadRequestOptions) => {
  const { file, onSuccess } = options;
  try {
    const data = { file };
    const response = await importDept(data);
    gridApi.query();
    onSuccess(response);
    ElMessage.success('数据导入成功');
  } catch {
    ElMessage.error('数据导入失败');
  }
};

/** 数据导出 */
const deptExportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    const response = await exportDept(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch (error) {
    console.error('数据导出失败:', error);
    ElMessage.error('数据导出失败');
  }
};
</script>
<template>
  <Page auto-content-height>
    <FormModal class="w-6/12">
      <FormView v-if="isView" :dept-id="deptId" ref="modalFormRef" />
      <FormEdit
        v-else
        :dept-id="deptId"
        ref="modalFormRef"
        @dept-form-submit-cancel="FormModalClose"
        @dept-form-submit-success="FormModalClose"
      />
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAction('', false, '新增')"
          v-access:code="'base:dept:edit:add'"
        >
          新增部门
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="下载导入模板"
          placement="top-start"
        >
          <ElButton circle @click="getDeptTemplate" class="mr-2">
            <template #icon><span class="iconfont">&#xe678;</span></template>
          </ElButton>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导入数据"
          placement="top-start"
        >
          <ElUpload
            :show-file-list="false"
            :http-request="deptImportHandle"
            accept=".xlsx,.xls"
          >
            <ElButton circle class="mr-2" v-access:code="'base:dept:import'">
              <template #icon>
                <span class="iconfont">&#xe621;</span>
              </template>
            </ElButton>
          </ElUpload>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="deptExportHandle"
            v-access:code="'base:dept:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
