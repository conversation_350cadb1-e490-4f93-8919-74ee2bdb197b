import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath } from '../path';

export namespace BaseDataCustomerApi {
  export interface BaseDataCustomer {
    [key: string]: any;
    records: Array<{
      [key: string]: any;
      tagList: Array<{ [key: string]: any }>;
    }>;
    total: string;
  }
}

/** 客户列表模板下载 */
export async function customerTemplate() {
  return requestClient.download(
    `${baseDataPath}/base/customer/customerTemplate`,
  );
}

/** 客户导入*/
export async function customerImport(data: { file: Blob | File }) {
  return requestClient.upload(
    `${baseDataPath}/base/customer/importCustomer`,
    data,
  );
}

/** 客户列表导出*/
export async function customerExport(params: Recordable<any>) {
  return requestClient.post<Blob>(
    `${baseDataPath}/base/customer/exportCustomer`,
    {
      ...params,
    },
    {
      responseReturn: 'body',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 获取客户分页列表*/
export async function getCustomerPageList(params: Recordable<any>) {
  return requestClient.post<Array<BaseDataCustomerApi.BaseDataCustomer>>(
    `${baseDataPath}/base/customer/getCustomerPage`,
    { ...params },
  );
}

/** 新增客户*/
export async function createCustomer(
  params: Omit<BaseDataCustomerApi.BaseDataCustomer, 'customerId'>,
) {
  return requestClient.post(
    `${baseDataPath}/base/customer/saveCustomer`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

/** 根据客户id停用客户*/
export async function disableCustomer(customerId: string) {
  return requestClient.get(
    `${baseDataPath}/base/customer/disableCustomer/${customerId}`,
  );
}

/** 根据客户id启用客户*/
export async function enableCustomer(customerId: string) {
  return requestClient.get(
    `${baseDataPath}/base/customer/enableCustomer/${customerId}`,
  );
}

// --------基本信息---------

/** 根据客户id或编号获取客户基本信息*/
export async function getCustomerBaseInfo(data: {
  customerCode?: string;
  customerId?: string;
}) {
  const { customerCode, customerId } = data;
  return requestClient.get(
    `${baseDataPath}/base/customer/getCustomerInfo?customerId=${customerId}&customerCode=${customerCode}`,
  );
}

/** 修改客户基本信息*/
export async function updateCustomerBaseInfo(data: any) {
  return requestClient.post(`${baseDataPath}/base/customer/modCustomer`, data);
}

// --------客户标签信息---------
/** 根据客户id查询客户标签列表 */
export async function getCustomerTag(data: { customerId: string }) {
  const { customerId } = data;
  return requestClient.get(
    `${baseDataPath}/base/customer/getCustomerTag/${customerId}`,
  );
}

/** 修改客户标签列表 */
export async function modCustomerTag(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/customer/tag/modCustomerTag`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------客户联系人信息---------
/** 根据客户id查询客户联系人信息列表 */
export async function getCustomerContacts(data: { customerId: string }) {
  const { customerId } = data;
  return requestClient.get(
    `${baseDataPath}/base/customer/getCustomerContacts/${customerId}`,
  );
}

/** 修改客户联系人信息列表 */
export async function modCustomerContacts(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/customer/contacts/modCustomerContacts`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------公司概况---------
/** 根据客户id查询客户公司概况信息*/
export async function getCustomerProfile(data: { customerId: string }) {
  const { customerId } = data;
  return requestClient.get(
    `${baseDataPath}/base/customer/getCustomerProfile/${customerId}`,
  );
}

/** 修改客户概况信息 */
export async function updateCustomerProfile(data: any) {
  return requestClient.post(
    `${baseDataPath}/base/customer/profile/modCustomerProfile`,
    data,
  );
}

// --------主要负责人信息---------
/** 根据客户id查询客户负责人信息列表 */
export async function getCustomerResponsible(data: { customerId: string }) {
  const { customerId } = data;
  return requestClient.get(
    `${baseDataPath}/base/customer/getCustomerResponsible/${customerId}`,
  );
}

/** 修改客户联系人信息列表 */
export async function modCustomerResponsible(params: any) {
  return requestClient.post(
    `${baseDataPath}/base/customer/responsible/modCustomerResponsible`,
    params,
    {
      // 转换为JSON
      transformRequest: [
        (data, headers) => {
          headers['Content-Type'] = 'application/json;charset=utf-8';
          return JSON.stringify(data);
        },
      ],
    },
  );
}

// --------交易信息---------
/** 根据客户id查询客户交易信息 */
export async function getCustomerTransaction(data: { customerId: string }) {
  const { customerId } = data;
  return requestClient.get(
    `${baseDataPath}/base/customer/getCustomerTransaction/${customerId}`,
  );
}
/** 修改客户交易记录信息 */
export async function updateCustomerTransaction(data: any) {
  return requestClient.post(
    `${baseDataPath}/base/customer/transaction/modCustomerTransaction`,
    data,
  );
}

/** 获取行业类别列表 */
export async function getIndustryList(data?: any) {
  return requestClient.post(
    `${baseDataPath}/base/industry/getIndustryList`,
    data,
  );
}
