<script lang="ts" setup>
import { defineProps } from 'vue';

import { useVbenForm } from '@girant/adapter';

import { useBaseInfoFormSchema } from './data';

const props = defineProps({
  formConfig: {
    default: () => {},
    type: Object,
  },
  isEdit: {
    default: false,
    type: Boolean,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  materialCode: {
    default: '',
    type: String,
  },
  materialId: {
    default: '',
    type: String,
  },
});

/** 基础资料表单 */
const [Form, FormApi] = useVbenForm({
  ...props.formConfig,
  schema: useBaseInfoFormSchema(props.isView),
});

const getFormValues = async () => {
  const formValues = await FormApi.getValues();
  return formValues;
};

const validateForm = async () => {
  const validatRes = await FormApi.validate();
  return validatRes;
};

// 统一提交处理
const onSubmitToInventory = async () => {
  const { valid } = await validateForm();
  if (!valid) {
    throw new Error('库存配置表单验证失败');
  }

  const mergedValues = await getFormValues();
  return mergedValues;
};

/** 获取数据 */
async function loadData(data: any) {
  FormApi.setValues(data);
}

defineExpose({
  Form,
  FormApi,
  loadData,
  onSubmitToInventory,
});
</script>

<template>
  <Form />
</template>
