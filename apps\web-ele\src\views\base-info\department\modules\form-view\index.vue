<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getDeptDetail } from '#/api/base-info';

import { useFormSchema } from './data';

const props = defineProps({
  /** 部门id */
  deptId: {
    default: '',
    type: String,
  },
});
const loading = ref(false);
/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
  },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

/** 获取数据 */
const loadData = async (deptId: string) => {
  try {
    loading.value = true;
    const data = await getDeptDetail({ deptId });
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(async () => {
  loadData(props.deptId);
});
defineExpose({
  formApi,
});
</script>
<template>
  <Form v-loading="loading" />
</template>
