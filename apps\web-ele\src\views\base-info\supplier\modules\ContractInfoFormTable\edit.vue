<script lang="ts" setup>
import type { RowType } from './data';

import { computed, defineProps, nextTick, onMounted, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { UploadFiles } from '@girant-web/upload-files-component';
import { ElButton, ElDatePicker, ElMessage } from 'element-plus';

import { getDictItemList } from '#/api';
import {
  getFrameworkList,
  invalid,
  modSupplierFramework,
} from '#/api/base-info';

import { useGridOptions } from './data';

const props = defineProps({
  customerCode: {
    default: '',
    type: String,
  },
  isEdit: {
    default: false,
    type: Boolean,
  },
  isView: {
    default: false,
    type: Boolean,
  },
  supplierId: {
    default: '',
    type: String,
  },
});

const loading = ref(false);
const gridTable = ref();
const GridApi = ref();
const gridTableData = ref<Array<RowType>>([]);

const startTimestamp = ref<number>();
const startTimeChange = (row: any, startTime: any) => {
  row.endTime = '';
  startTimestamp.value = new Date(startTime).getTime();
  // row.endTime = isEmpty(startTime) ? '' : startTime;
};

const disabledDate = computed(() => {
  return (time: Date) => {
    return time.getTime() < startTimestamp.value!;
  };
});

// 定义字典项接口
interface DictItem {
  [key: string]: any;
  dictLabel: string;
  dictValue: string;
}

// 定义选项接口
interface OptionItem {
  label: string;
  value: string;
}

// 定义表格选项插槽配置接口
interface TableSlotOption {
  dictItem: string; // 字典类型编码
  options: OptionItem[]; // 选项数组，需要明确类型
  slotName: string; // 插槽名称
}

// 使用定义的接口进行类型声明
const tableOptsToSlot = ref<TableSlotOption[]>([
  {
    dictItem: 'baseSettlementMethod',
    options: [],
    slotName: 'settlementMethod',
  },
  {
    dictItem: 'baseInvoiceType',
    options: [],
    slotName: 'invoiceType',
  },
  {
    dictItem: 'baseDeliveryMethod',
    options: [],
    slotName: 'deliveryMethodList',
  },
  {
    dictItem: 'baseVatInvoiceRate',
    options: [],
    slotName: 'vatInvoiceRate',
  },
]);

// 然后在onMounted中遍历加载字典数据
onMounted(async () => {
  await Promise.all(
    tableOptsToSlot.value.map(async (item) => {
      try {
        // 调用API获取字典数据
        const dictData = await getDictItemList(item.dictItem);

        // 将字典数据转换为选项格式并存储到options中
        item.options = dictData.map((dictItem: DictItem) => ({
          label: dictItem.dictLabel,
          value: dictItem.dictValue,
        }));
      } catch (error) {
        console.error(`加载字典 ${item.dictItem} 失败:`, error);
        // 出错时设置为空数组，确保不会导致渲染错误
        item.options = [];
      }
    }),
  );
});

const defaulBtns = [
  {
    code: 'edit',
    label: '编辑',
    type: 'primary',
  },
  {
    code: 'upload',
    label: '上传附件',
    type: 'primary',
  },
];

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onBeforeClose: async () => {
    currentSerialNumber.value = '';
    currentRow.value = null;
    const isCompleted = await uploadSerialNumberRef.value?.getCompleteStatus();
    if (!isCompleted) {
      ElMessage.error('等待附件上传完成');
      return false;
    }
    return true;
  },
});
const key = ref(0);
// 附件上传
const uploadSerialNumberRef = ref();
// 当前修改row的流水号
const currentSerialNumber = ref('');
// 当前修改的row
const currentRow = ref(null);

const filesSubmitSuccess = (field: string, res: any) => {
  setFormTableRow(currentRow.value, { [field]: res.serialNumber });
};

function onActionClick(code: string, row: any) {
  currentRow.value = row;
  key.value++;
  switch (code) {
    case 'cancel': {
      gridTable.value.revertRow(row);
      gridTable.value.cancelRow(row);
      break;
    }
    case 'cancellation': {
      rowToInvalid(row);
      break;
    }
    case 'delete': {
      gridTable.value.removeRow(row);
      break;
    }
    case 'edit': {
      gridTable.value.editRow(row);
      break;
    }
    case 'save': {
      onSubmitRow(row);
      break;
    }

    case 'upload': {
      if (row.serialNumber) {
        currentSerialNumber.value = row.serialNumber;
      }
      drawerApi.open();
      break;
    }
  }
}

// row赋值
const setFormTableRow = (row: any, newRow: any) => {
  GridApi.value.setRow(row, newRow);
};

// const isNewRow = (row: any) => {
//   return GridApi.value.isInsertByRow(row);
// };

const isEditRow = (row: any) => {
  return GridApi.value.isEditByRow(row);
};

const isUpdateRow = (row: any) => {
  return GridApi.value.isUpdateByRow(row);
};

// 表单表格
const gridOptions = useGridOptions(props.isEdit);

/** 获取数据 */
async function loadData(supplierId: string) {
  try {
    loading.value = true;
    const FormTableRes = await getFrameworkList({
      supplierId,
    });

    if (!FormTableRes) {
      return;
    }

    const formatFormTableRes = FormTableRes.map((item: any) => {
      item.deliveryMethodList = item.deliveryMethodList
        ? item.deliveryMethodList.map((item: any) => item.deliveryMethod)
        : [];

      return item;
    });

    gridTableData.value = FormTableRes;

    nextTick(() => {
      gridTable.value.setTableData(formatFormTableRes);
    });

    return formatFormTableRes;
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  nextTick(() => {
    const [, VbenGridApi] = gridTable.value.getGridTableIns();
    VbenGridApi.value = VbenGridApi;
    GridApi.value = VbenGridApi.grid;

    if (!props.isView) {
      gridTable.value.setTableData([]);
    }
  });

  if (!isEmpty(props.supplierId)) {
    loadData(props.supplierId);
  }
});

// 获取表格数据
const getTableData = async () => {
  const tableData = await gridTable.value.getTableData();
  return tableData;
};

// 校验表格数据
const tableValidate = async () => {
  const tableValidateRes = await gridTable.value.tableValidate(true);
  return tableValidateRes;
};

// 校验行数据
const tableValidateToRow = async (row: any) => {
  const rowValidateRes = await GridApi.value.validate(row);

  return rowValidateRes;
};

// 单行提交
const onSubmitRow = async (row: any) => {
  const rowValidateRes = await tableValidateToRow(row);

  if (rowValidateRes) {
    ElMessage.error('请正确填写表单');
    return;
  }

  loading.value = true;

  const mergedValues = {
    ...row,
    supplierId: props.supplierId,
  };

  const newRow = {
    ...row,
    supplierId: props.supplierId,
  };

  const deliveryMethodList = mergedValues.deliveryMethodList || [];

  mergedValues.deliveryMethodList =
    deliveryMethodList.length > 0 ? deliveryMethodList.join(',') : '';

  delete mergedValues.isValid;

  try {
    await modSupplierFramework(mergedValues);
    GridApi.value.reloadRow(row, newRow);
    gridTable.value.cancelRow(row);
    ElMessage.success('修改成功');
  } catch (error) {
    console.error('修改失败:', error);
    ElMessage.error('修改失败');
  } finally {
    loading.value = false;
  }
};

// 作废
const rowToInvalid = async (row: any) => {
  loading.value = true;
  const newRow = {
    ...row,
    isValid: false,
  };
  try {
    await invalid({ contractId: row.contractId });

    GridApi.value.reloadRow(row, newRow);
    gridTable.value.cancelRow(row);
    ElMessage.success('操作成功');
  } catch (error) {
    console.error('操作成功:', error);
    ElMessage.error('操作成功');
  } finally {
    loading.value = false;
  }
};

const onSubmitToFormTable = async () => {
  try {
    loading.value = true;
    const tableData = await getTableData();

    // 空表单检查 - 仅在新建模式下
    if (tableData.length === 0) {
      return null;
    }

    // 表单验证
    const tableValidateRes = await tableValidate();
    if (tableValidateRes) {
      throw new Error('采购框架合同信息表单验证失败');
    }

    const formatTableData = tableData.map((item: any) => {
      return item;
    });

    return formatTableData;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
  } finally {
    loading.value = false;
  }
};

defineExpose({
  onSubmitToFormTable,
});
</script>

<template>
  <div v-loading="loading">
    <Drawer class="w-1/3" title="附件上传">
      <UploadFiles
        :key="key"
        :mode="isView ? 'readMode' : 'editMode'"
        ref="uploadSerialNumberRef"
        :show-operate-region="false"
        :allowed-formats="['jpg', 'pdf', 'png']"
        :text-config="{
          formatText: '支持格式:pdf,jpg,png',
        }"
        :serial-number="currentSerialNumber"
        @file-submit-success="filesSubmitSuccess('serialNumber', $event)"
      />
    </Drawer>
    <DynamicTable ref="gridTable" :grid-options="gridOptions" class="border">
      <template #startTime="{ row }">
        <ElDatePicker
          class="!w-full"
          v-model="row.startTime"
          placeholder="请选择"
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="startTimeChange(row, $event)"
        />
      </template>

      <template #endTime="{ row }">
        <ElDatePicker
          class="!w-full"
          v-model="row.endTime"
          placeholder="请选择"
          format="YYYY-MM-DD"
          type="date"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          :default-value="new Date(row.startTime)"
          :disabled="!row.startTime"
        />
      </template>

      <template
        v-for="slotItem in tableOptsToSlot"
        :key="slotItem.slotName"
        #[slotItem.slotName]="{ row, column }"
      >
        <el-select
          v-model="row[column.field]"
          placeholder="请选择"
          :multiple="slotItem.slotName === 'deliveryMethodList'"
          :collapse-tags="true"
          :disabled="!isEditRow(row)"
        >
          <el-option
            v-for="option in slotItem.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>

      <template #Operation="{ row }">
        <div style="display: flex; place-items: center center">
          <ElButton
            v-for="btn in defaulBtns"
            :key="btn.code"
            :type="btn.type as any"
            :link="true"
            size="small"
            @click="onActionClick(btn.code, row)"
          >
            {{ btn.label }}
          </ElButton>

          <ElButton
            :link="true"
            size="small"
            type="danger"
            @click="onActionClick('cancellation', row)"
            v-if="row.contractId && row.isValid"
          >
            作废
          </ElButton>

          <ElButton
            :link="true"
            size="small"
            type="info"
            @click="onActionClick('cancel', row)"
            v-if="isUpdateRow(row)"
          >
            取消编辑
          </ElButton>

          <el-popconfirm
            title="删除"
            placement="top-start"
            @confirm="onActionClick('delete', row)"
            v-if="!row.contractId"
          >
            <template #reference>
              <ElButton :link="true" size="small" type="danger">
                删除
              </ElButton>
            </template>
          </el-popconfirm>

          <el-popconfirm
            title="保存"
            placement="top-start"
            @confirm="onActionClick('save', row)"
            v-if="row.contractId || isEdit"
          >
            <template #reference>
              <ElButton :link="true" size="small" type="success">
                保存
              </ElButton>
            </template>
          </el-popconfirm>
        </div>
      </template>
    </DynamicTable>
  </div>
</template>
