<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';
import type { UploadRequestOptions } from 'element-plus';

import type { Recordable } from '@vben/types';

import type { BaseDataStaffApi } from '#/api/base-info';

import { computed, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElDatePicker,
  ElMessage,
  ElMessageBox,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import {
  disableStaff,
  enableStaff,
  getStaffPageList,
  staffExport,
  staffImport,
  staffTemplate,
} from '#/api/base-info';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const staffId = ref('');
const isView = ref(false);
const modalFormRef = ref();

/** 入职日期 */
const hireTime = ref({
  // 开始时间
  startTime: '',
  // 结束时间
  endTime: '',
});
/** 离职日期 */
const terminationTime = ref({
  // 开始时间
  startTime: '',
  // 结束时间
  endTime: '',
});

/**
 * 日期禁用函数
 * 开始时间不能大于结束时间
 * @param isEnd 是否是结束时间
 */
const createDisabledDate = (isEnd: boolean, thisTime: any) => {
  return (time: Date) => {
    if (!thisTime.endTime && !isEnd) {
      return false;
    }
    // 是结束时间
    return isEnd
      ? time.getTime() < new Date(thisTime.startTime).getTime()
      : time.getTime() > new Date(thisTime.endTime).getTime();
  };
};

/** 操作 */
const onActionClick = (
  e: OnActionClickParams<BaseDataStaffApi.BaseDataStaff>,
) => {
  switch (e.code) {
    case 'edit': {
      onAction(e.row.staffId, false, '编辑');
      break;
    }
    case 'view': {
      onAction(e.row.staffId, true, '查看');
      break;
    }
  }
};

/** 切换员工可用状态 */
const onStatusChange = async (
  newStatus: Boolean,
  row: BaseDataStaffApi.BaseDataStaff,
) => {
  const isEnable: Recordable<string> = {
    false: '已启用',
    true: '已禁用',
  };
  try {
    await confirm(
      `您要将 【${row.staffName}】 的状态切换为 【${isEnable[newStatus.toString()]}】 吗？`,
    );
    await (row.isEnable ? disableStaff(row.staffId) : enableStaff(row.staffId));
    ElMessage({
      type: 'success',
      message: row.isEnable ? '停用成功' : '启用成功',
    });
    return true;
  } catch {
    return false;
  }
};
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    hireTime.value = {
      startTime: '',
      endTime: '',
    };
    terminationTime.value = {
      startTime: '',
      endTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    modalFormRef.value.onSubmit();
  },
  showCancelButton: true,
  showConfirmButton: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset,
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 70,
    },
    schema: useGridFormSchema(),
    showCollapseButton: true,
    collapsed: true,
    collapsedRows: 2,
    wrapperClass:
      'grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  },
  gridOptions: {
    border: true,
    cellConfig: {
      height: 60,
    },
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: any) => {
          const formValues = await gridApi.formApi.getValues();
          return await getStaffPageList({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            startHireTime: hireTime.value.startTime,
            endHireTime: hireTime.value.endTime,
            startTerminationTime: terminationTime.value.startTime,
            endTerminationTime: terminationTime.value.endTime,
          });
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'staffId',
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<BaseDataStaffApi.BaseDataStaff>,
});

/** 确认对话框抽象*/
const confirm = (content: string) => {
  return ElMessageBox.confirm(content, '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  });
};

/**
 * 表格操作
 * @param id
 * @param view 是否是查看状态
 * @param title 标题
 */
const onAction = (
  id: string = '',
  view: boolean = false,
  title: string = '操作',
) => {
  staffId.value = id;
  isView.value = view;
  formModalApi
    .setState({
      showConfirmButton: !view,
      title,
    })
    .open();
};

/** 关闭表单 */
const FormModalClose = async () => {
  formModalApi.close();
  gridApi.query();
};

/** 下载模板 */
const getStaffTemplate = async () => {
  try {
    const response = await staffTemplate();
    downloadFileFromResponse(response);
  } catch {
    ElMessage.error('文件下载失败：');
  }
};

/** 数据导入*/
const staffImportHandle = async (options: UploadRequestOptions) => {
  const { file, onSuccess } = options;
  try {
    const data = { file };
    const response = await staffImport(data);
    gridApi.query();
    onSuccess(response);
    ElMessage.success('数据导入成功');
  } catch {
    ElMessage.error('数据导入失败');
  }
};

/** 数据导出 */
const staffExportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    const response = await staffExport({
      ...formValues,
      startHireTime: hireTime.value.startTime,
      endHireTime: hireTime.value.endTime,
      startTerminationTime: terminationTime.value.startTime,
      endTerminationTime: terminationTime.value.endTime,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
};

const workStatusType = computed(
  () => (workStatus: string) => (workStatus === '00' ? 'success' : 'danger'),
);
const workStatusText = computed(
  () => (workStatusLabel: string) => workStatusLabel || '未知',
);
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-8/12">
      <Form
        ref="modalFormRef"
        :staff-id="staffId"
        @staff-form-submit-cancel="FormModalClose"
        @staff-form-submit-success="FormModalClose"
        :is-view="isView"
      />
    </FormModal>
    <Grid>
      <template #form-hireTime>
        <ElDatePicker
          v-model="hireTime.startTime"
          type="date"
          :disabled-date="createDisabledDate(false, hireTime)"
          value-format="YYYY-MM-DD"
          placeholder="开始日期"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="hireTime.endTime"
          type="date"
          :disabled-date="createDisabledDate(true, hireTime)"
          value-format="YYYY-MM-DD"
          placeholder="结束日期"
        />
      </template>
      <template #form-terminationTime>
        <ElDatePicker
          v-model="terminationTime.startTime"
          type="date"
          :disabled-date="createDisabledDate(false, terminationTime)"
          value-format="YYYY-MM-DD"
          placeholder="开始日期"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="terminationTime.endTime"
          type="date"
          :disabled-date="createDisabledDate(true, terminationTime)"
          value-format="YYYY-MM-DD"
          placeholder="结束日期"
        />
      </template>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAction('', false, '新增')"
          v-access:code="'base:employee:edit:add'"
        >
          新增员工
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="下载导入模板"
          placement="top-start"
        >
          <ElButton
            circle
            @click="getStaffTemplate"
            class="mr-2"
            v-access:code="'base:employee:import'"
          >
            <template #icon><span class="iconfont">&#xe678;</span></template>
          </ElButton>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导入数据"
          placement="top-start"
        >
          <ElUpload
            :show-file-list="false"
            :http-request="staffImportHandle"
            accept=".xlsx,.xls"
          >
            <ElButton
              circle
              class="mr-2"
              v-access:code="'base:employee:import'"
            >
              <template #icon>
                <span class="iconfont">&#xe621;</span>
              </template>
            </ElButton>
          </ElUpload>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="staffExportHandle"
            v-access:code="'base:employee:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
      <template #workStatus="{ row }">
        <ElTag :type="workStatusType(row.workStatus)">
          {{ workStatusText(row.workStatusLabel) }}
        </ElTag>
      </template>
      <template #hireTime="{ row }">
        {{ row.hireTime }}~{{ row.terminationTime || '至今' }}
      </template>
    </Grid>
  </Page>
</template>
