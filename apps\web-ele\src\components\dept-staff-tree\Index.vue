<script setup lang="ts">
import type { ExpandTrigger } from 'element-plus';

import type { PropType } from 'vue';

import { computed, defineEmits, defineProps, onMounted, ref } from 'vue';

import { ElCascader } from 'element-plus';

import { getDeptStaffTree } from '#/api/components';

// 定义人员和部门接口
type Staff = {
  staffCode: string;
  staffId: number;
  staffName: string;
};

type Dept = {
  deptAbbr: string;
  deptCode: string;
  deptDescribe: string;
  deptId: number;
  deptName: string;
  deptSort: number;
  parentId: number;
  parentName: string;
  serialNumber: number;
  staffList: Staff[];
};

// 定义级联选择器选项接口
type CascaderOption = {
  [key: string]: any;
  children?: CascaderOption[];
  disabled?: boolean;
  label: string;
  leaf?: boolean;
  value: number | string;
};

// 定义组件属性
const props = defineProps({
  // 绑定的值，可以是数组、数字或字符串
  value: {
    type: [Array, Number, String] as PropType<
      Array<number | string> | number | string
    >,
    default: () => [],
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 是否自动加载数据
  autoLoad: {
    type: Boolean,
    default: true,
  },
  // 折叠标签的最大数量
  maxCollapseTags: {
    type: Number,
    default: 2,
  },
  // 是否严格遵守父子节点不关联的选择逻辑
  checkStrictly: {
    type: Boolean,
    default: false,
  },
  // 是否返回选中节点的完整路径
  emitPath: {
    type: Boolean,
    default: false,
  },
  // 是否显示没有员工的部门
  showEmptyDept: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:value', 'loadSuccess', 'loadFail']);

const cascaderRef = ref<InstanceType<typeof ElCascader>>();
const deptList = ref<Dept[]>([]);
const loading = ref(false);

const selectedValue = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit('update:value', value);
  },
});

const handleChange = (value: any) => {
  selectedValue.value = value;
};

const loadDeptList = async () => {
  loading.value = true;
  try {
    const res = await getDeptStaffTree({
      isQueryStaff: true,
    });
    deptList.value = res || [];
    emit('loadSuccess', deptList.value);
  } catch (error) {
    console.error('加载部门数据失败', error);
    emit('loadFail', error);
  } finally {
    loading.value = false;
  }
};

// 如果启用自动加载，则在挂载后加载数据
onMounted(() => {
  if (props.autoLoad) {
    loadDeptList();
  }
});

const cascaderOptions = computed(() => {
  if (!deptList.value || deptList.value.length === 0) return [];

  const options: CascaderOption[] = [];
  for (const dept of deptList.value) {
    if (
      !props.showEmptyDept &&
      (!dept.staffList || dept.staffList.length === 0)
    ) {
      continue;
    }

    const deptNode: CascaderOption = {
      value: `dept_${dept.deptId}`,
      label: dept.deptName,
      children: [],
      // disabled: true, // 部门节点不可选
    };

    if (dept.staffList && dept.staffList.length > 0) {
      deptNode.children = dept.staffList.map((staff) => ({
        value: staff.staffId,
        label: staff.staffName,
      }));
    }

    options.push(deptNode);
  }
  return options;
});

const cascaderProps = computed(() => ({
  expandTrigger: 'click' as ExpandTrigger,
  multiple: props.multiple,
  value: 'value',
  label: 'label',
  emitPath: props.emitPath,
  checkStrictly: props.checkStrictly,
}));
</script>

<template>
  <ElCascader
    v-model="selectedValue"
    :options="cascaderOptions"
    :props="cascaderProps"
    @change="handleChange"
    ref="cascaderRef"
    :loading="loading"
    filterable
    collapse-tags
    collapse-tags-tooltip
    :max-collapse-tags="props.maxCollapseTags"
  />
</template>
