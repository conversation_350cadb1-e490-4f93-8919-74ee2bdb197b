<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';
import type { UploadRequestOptions } from 'element-plus';

import type { Recordable } from '@vben/types';

import type { BaseDataDeptApi, BaseDataPositionApi } from '#/api/base-info';

import { nextTick, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElButton, ElMessage, ElMessageBox } from 'element-plus';

import {
  disablePosition,
  enablePosition,
  exportPosition,
  getAllDeptTree,
  getPositionList,
  importPosition,
  positionTemplate,
} from '#/api/base-info';

import { useGridFormSchema, useTreeColumns } from './data';
import FormEdit from './modules/form-edit/index.vue';
import FormView from './modules/form-view/index.vue';

/** 岗位id */
const positionId = ref('');
/** 是否是查看状态*/
const isView = ref(false);
/** 模态框表单 */
const modalFormRef = ref();
/** 部门树数据 */
const deptTreeData = ref<BaseDataDeptApi.deptTreeType[]>([]);
/** 岗位数据 */
const positionData = ref([]);

/** 切换岗位可用状态 */
const onStatusChange = async (
  newStatus: Boolean,
  row: BaseDataPositionApi.BaseDataPosition,
) => {
  const isEnable: Recordable<string> = {
    false: '已禁用',
    true: '已启用',
  };
  try {
    await confirm(
      `您要将 【${row.positionName}】 的状态切换为 【${isEnable[newStatus.toString()]}】 吗？`,
    );
    if (row.isEnable) {
      const res = await disablePosition(row.positionId);
      const isSuccess = await DisableProcessing(row, res);
      if (isSuccess) {
        // 刷新表格
        await gridApi.query();
      }
      return isSuccess;
    } else {
      await enablePosition(row.positionId);
      ElMessage.success('启用成功');
      return true;
    }
  } catch {
    return false;
  }
};
/** 操作按钮 */
const onActionClick = (
  e: OnActionClickParams<BaseDataPositionApi.BaseDataPosition>,
) => {
  switch (e.code) {
    case 'edit': {
      onAction(e.row.positionId, false, '编辑');
      break;
    }
    case 'view': {
      onAction(e.row.positionId, true, '查看');
      break;
    }
  }
};
// 查询表单
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[70px]',
    },
    schema: useGridFormSchema(),
    showCollapseButton: false,
    wrapperClass:
      'grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  },
  gridOptions: {
    border: true,
    columns: useTreeColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    // 由于设置enabled: false, 导致数据显示不出来,可能是我使用了proxyConfig的原因，所以折中设置autoHidden来隐藏
    pagerConfig: {
      autoHidden: true, // 当只有一页时隐藏分页器
      enabled: true,
      pageSize: 99_999, // 设置每页大小
    },
    proxyConfig: {
      ajax: {
        query: async ({ form }, formValues: Recordable<any>) => {
          // 要搜索岗位的数据
          const positionValues = { ...formValues };
          // 岗位数据
          const positionRes = await getPositionList({
            ...form,
            ...positionValues,
          });
          // 获取岗位中的部门id 有数据的部门id
          const deptIds = [
            ...new Set(
              positionRes.map((item: { deptId: string }) => item.deptId),
            ),
          ];
          if (deptIds.length === 0) {
            return {
              records: [],
            };
          }
          // 部门树数据
          const deptRes = await getAllDeptTree({
            deptIdList: positionValues.deptIdList,
          });
          deptTreeData.value = deptRes;
          positionData.value = positionRes;
          const newData = setFromData(
            deptRes,
            positionRes,
            deptIds as number[],
          );
          return {
            records: newData, // 确保返回数据结构正确
          };
        },
        querySuccess: async () => {
          // 获取数据后，展开所有节点
          await nextTick();
          gridApi.grid.setAllTreeExpand(true);
        },
      },
      response: {
        result: 'records',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    spanMethod({ column, row }: { column: any; row: any }) {
      if (column.type !== 'seq') {
        // 如果是部门，且是第一列，合并7列
        if (row.type === 'dept' && column.title === '部门') {
          return { colspan: 8, rowspan: 1 };
        }
        if (row.type === 'position') {
          return { colspan: 1, rowspan: 1 };
        }
        return { colspan: 0, rowspan: 0 };
      }
      return { colspan: 1, rowspan: 1 };
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
    treeConfig: {
      rowField: 'dept',
    },
  } as VxeTableGridOptions<BaseDataPositionApi.BaseDataPosition>,
});

// 模态框
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    modalFormRef.value.formApi.validateAndSubmitForm();
  },
  showCancelButton: true,
  showConfirmButton: true,
});

/** 确认对话框抽象*/
const confirm = (content: string) => {
  return ElMessageBox.confirm(content, '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  });
};

/** 根据停用返回的结果做出不同的操作*/
const DisableProcessing = async (row: any, data: any) => {
  if (data.code === 10_002_000) {
    ElMessage.success('停用成功');
    return true;
  }
  // 10_005_010  判断是否存在未停用的子岗位或员工
  else if (data.code === 10_005_010) {
    // 存在未停用的员工，则提示用户不可停用
    if (data.data.staffCount > 0) {
      const text = `当前有${data.data.staffCount}位员工任职该岗位，不可停用`;
      await confirm(`${text}`);
      return false;
    }
    // 不存在未停用的员工 则提示用户是否停用
    else {
      let text = '当前岗位是';
      let positionText = '';
      data.data.positionNameList?.forEach((item: string) => {
        positionText += `【${item}】`;
      });
      text += `${positionText}的上级岗位，是否强制停用？停用后${positionText}将无上级岗位。`;
      await confirm(text);
      const res = await disablePosition(row.positionId, true);
      // 检查停用结果
      await DisableProcessing(row, res);
      return true;
    }
  } else {
    ElMessage.error('停用失败');
    return false;
  }
};

/** 检查部门树是否包含指定部门id，并返回包含该部门的路径 */
const findDeptPath = (data: any[], deptId: number): any[] => {
  if (!data?.length) return [];
  for (const node of data) {
    // 如果当前节点是目标部门，返回包含当前节点的路径
    if (node.deptId === deptId) {
      return [node];
    }
    // 递归检查子节点
    const path = findDeptPath(node.children || [], deptId);
    if (path.length > 0) {
      // 如果在子节点中找到目标部门，将当前节点添加到路径前面
      return [node, ...path];
    }
  }
  // 如果遍历完整个树都没有找到，返回空数组
  return [];
};
/** 过滤部门树，只保留包含指定部门id的路径 */
const filterDeptTree = (data: any[], deptIds: number[]): any[] => {
  // 存储所有需要保留的路径
  const allPaths: any[] = [];
  // 为每个 deptId 查找路径
  for (const deptId of deptIds) {
    const path = findDeptPath(data, deptId);
    if (path.length > 0) {
      allPaths.push(path);
    }
  }
  // 如果没有找到任何路径，返回空数组
  if (allPaths.length === 0) {
    return [];
  }
  // 构建结果，合并所有路径到一个树中
  const result: any[] = [];
  const rootDeptIds = new Set(allPaths.map((path) => path[0].deptId));
  for (const path of allPaths) {
    const rootDeptId = path[0].deptId;
    if (!rootDeptIds.has(rootDeptId)) {
      continue;
    }
    // 查找是否已有该根节点
    let rootNode = result.find((node) => node.deptId === rootDeptId);
    if (!rootNode) {
      rootNode = { ...path[0], children: [] };
      result.push(rootNode);
    }
    // 合并路径到根节点的子节点中
    mergePathToNode(rootNode, path.slice(1));
  }
  return result;
  // 辅助函数：将路径合并到指定节点的子节点中
  function mergePathToNode(node: any, path: any[]): void {
    if (path.length === 0) return;
    const nextNode = path[0];
    const existingChild = node.children.find(
      (child: { deptId: any }) => child.deptId === nextNode.deptId,
    );
    if (existingChild) {
      mergePathToNode(existingChild, path.slice(1));
    } else {
      const newNode = { ...nextNode, children: [] };
      node.children.push(newNode);
      mergePathToNode(newNode, path.slice(1));
    }
  }
};

/**
 * 处理表单的数据格式
 * @param data 部门树数据
 * @param allPositions 岗位数据
 * @param deptIds 有岗位的部门id列表
 */
const setFromData = (data: any[], allPositions?: any[], deptIds?: number[]) => {
  const filteredTree = filterDeptTree(data, deptIds!);
  // 构建岗位映射表 将岗位数据按 deptId 分组，形成 部门ID -> 岗位数组 的映射表
  const positionMap = new Map<number, any[]>();
  if (allPositions) {
    for (const position of allPositions) {
      if (!positionMap.has(position.deptId)) {
        positionMap.set(position.deptId, []);
      }
      positionMap.get(position.deptId)!.push({
        ...position,
        type: 'position',
      });
    }
  }
  // 将岗位数据挂载到对应部门
  const result = [...filteredTree];
  // 递归函数：挂载岗位数据
  const mountPositions = (deptNode: any) => {
    // 获取当前部门的岗位数据
    const positions = positionMap.get(deptNode.deptId) || [];
    // 设置部门信息
    deptNode.dept = deptNode.deptName;
    // 设置部门类型
    deptNode.type = 'dept';
    // 将岗位数据挂载到部门的 children 中
    deptNode.children = [...positions, ...(deptNode.children || [])];
    // 递归处理子部门
    if (deptNode.children?.length) {
      for (const child of deptNode.children) {
        if (child.type !== 'position') {
          mountPositions(child);
        }
      }
    }
  };
  // 处理结果树中的每个部门节点
  for (const dept of result) {
    mountPositions(dept);
  }
  return result;
};

/**
 * 根据岗位ID获取部门名称
 * @param data 岗位数据
 * @param targetId 目标岗位ID
 * @returns 部门名称
 */
const getDeptNameById = (data: any[], targetId: string): string => {
  // 基础校验
  if (!data?.length) return '';
  for (const node of data) {
    // 找到目标节点
    if (node.positionId === targetId) {
      return node.deptName;
    }
  }
  return ''; // 未找到
};

/**
 * 表格操作
 * @param id
 * @param view 是否是查看状态
 * @param title 标题
 */
const onAction = (
  id: string = '',
  view: boolean = false,
  title: string = '操作',
) => {
  // 这里根据需求进行修改
  positionId.value = id;
  isView.value = view;
  formModalApi
    .setState({
      showConfirmButton: !view,
      title,
    })
    .open();
};
/** 关闭 */
const FormModalClose = async () => {
  formModalApi.close();
  await gridApi.query();
};
/** 下载模板 */
const getPositionTemplate = async () => {
  try {
    const response = await positionTemplate();
    downloadFileFromResponse(response);
  } catch (error) {
    console.error('文件下载失败：', error);
  }
};

/** 数据导入*/
const positionImportHandle = async (options: UploadRequestOptions) => {
  const { file, onSuccess } = options;
  try {
    const data = { file };
    const response = await importPosition(data);
    gridApi.query();
    onSuccess(response);
    ElMessage.success('数据导入成功');
  } catch {
    ElMessage.error('数据导入失败');
  }
};
/** 数据导出 */
const positionExportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    const response = await exportPosition(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch (error) {
    console.error('数据导出失败:', error);
    ElMessage.error('数据导出失败');
  }
};
</script>
<template>
  <Page auto-content-height>
    <FormModal class="w-6/12">
      <FormView v-if="isView" ref="modalFormRef" :position-id="positionId" />
      <FormEdit
        v-else
        ref="modalFormRef"
        :position-id="positionId"
        @position-form-submit-cancel="FormModalClose"
        @position-form-submit-success="FormModalClose"
      />
    </FormModal>
    <Grid>
      <template #upPositionName="{ row }">
        {{
          row.parentId && getDeptNameById(positionData, row.parentId)
            ? `【${getDeptNameById(positionData, row.parentId)}】`
            : ''
        }}
        {{ row.upPositionName }}
      </template>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAction('', false, '新增')"
          v-access:code="'base:position:edit:add'"
        >
          新增岗位
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="下载导入模板"
          placement="top-start"
        >
          <ElButton circle @click="getPositionTemplate" class="mr-2">
            <template #icon><span class="iconfont">&#xe678;</span></template>
          </ElButton>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导入岗位列表"
          placement="top-start"
        >
          <ElUpload
            :show-file-list="false"
            :http-request="positionImportHandle"
            accept=".xlsx,.xls"
          >
            <ElButton circle class="mr-2">
              <template #icon>
                <span class="iconfont">&#xe621;</span>
              </template>
            </ElButton>
          </ElUpload>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出岗位列表"
          placement="top-start"
        >
          <ElButton
            circle
            @click="positionExportHandle"
            v-access:code="'base:position:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
